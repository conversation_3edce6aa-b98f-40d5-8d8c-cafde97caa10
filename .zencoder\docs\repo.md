# Exception Tracker (Extrack) Information

## Summary
Extrack is a comprehensive exception tracking and knowledge management system designed to help development teams track, manage, and resolve software exceptions and issues efficiently. The application provides a centralized platform for documenting issues, solutions, and knowledge base articles.

## Structure
- **app/**: Next.js app directory containing pages and routes
- **backend/**: FastAPI Python backend with database models and API endpoints
- **components/**: React components for the frontend UI
- **docs/**: Project documentation including architecture and API references
- **lib/**: Utility libraries and API client functions
- **public/**: Static assets for the frontend
- **styles/**: CSS and styling files
- **types/**: TypeScript type definitions
- **utils/**: Utility functions for the frontend

## Language & Runtime
**Frontend**:
- **Language**: TypeScript/JavaScript
- **Framework**: Next.js (v15.3.0-canary)
- **Package Manager**: npm

**Backend**:
- **Language**: Python
- **Framework**: FastAPI (v0.115.11)
- **Database ORM**: SQLAlchemy (v2.0.38)

## Dependencies

### Frontend Dependencies
- **UI Components**: Radix UI primitives, shadcn/ui
- **Styling**: Tailwind CSS (v3.4.1)
- **State Management**: React Context API, React Query
- **Form Handling**: React Hook Form with Zod validation
- **Authentication**: NextAuth.js

### Backend Dependencies
- **Web Server**: Uvicorn (v0.34.0)
- **Database**: MySQL (via mysql-connector-python)
- **Authentication**: JWT (via python-jose)
- **Password Hashing**: Passlib with bcrypt
- **Environment Variables**: python-dotenv
- **Data Validation**: Pydantic (v2.10.6)

## Build & Installation

### Frontend
```bash
# Install dependencies
npm install

# Development mode
npm run dev:frontend

# Production build
npm run build
npm start
```

### Backend
```bash
# Install dependencies
pip install -r requirements.txt

# Run database migrations
cd backend
alembic upgrade head

# Start development server
python run.py
```

## Docker
**Configuration**: Docker Compose setup in `compose.yaml`
```bash
# Start the application with Docker Compose
docker-compose up
```

The service is configured to build from the current context and expose port 8000.

## Database
- **Type**: MySQL
- **Connection**: SQLAlchemy with PyMySQL driver
- **Configuration**: Environment variables for connection details (DB_USER, DB_PASSWORD, DB_HOST, DB_NAME)
- **Default Database**: central_db

## API Endpoints

### Main API Routes
- Authentication: JWT-based user authentication
- Projects: Project management and access control
- Issues: Exception tracking and management
- Knowledge Base: Documentation and solutions
- Search: Global and project-specific search functionality
- Favorites: User favorites management

## Testing
**Backend**:
- **Framework**: pytest (v8.3.5)
- **Test Location**: backend/tests/
- **Run Command**:
```bash
cd backend
pytest
```

## Development Workflow
1. Run frontend and backend concurrently:
```bash
npm run dev
```

2. Access the application at http://localhost:3000
3. Backend API is available at http://localhost:8000
4. API documentation available at http://localhost:8000/docs (Swagger UI)