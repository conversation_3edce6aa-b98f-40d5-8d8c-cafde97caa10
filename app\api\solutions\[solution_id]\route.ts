import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || 'http://localhost:8000';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ solution_id: string }> }
) {
  const { solution_id: id } = await params;
  try {
    // Check if id is a valid integer
    const solutionIdNum = parseInt(id, 10);
    if (isNaN(solutionIdNum) || solutionIdNum <= 0) {
      return NextResponse.json(
        { error: 'Invalid solution ID. Must be a positive integer.' },
        { status: 400 }
      );
    }
    
    // Get the authentication token
    const token = await getToken({ req: request as any });
    if (!token || !token['accessToken']) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Make the request to the backend API
    if (!id) {
      return NextResponse.json(
        { error: 'Solution ID is required' },
        { status: 400 }
      );
    }

    const response = await fetch(`${API_BASE_URL}/solutions/${encodeURIComponent(id)}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token['accessToken']}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      // Add timeout to prevent hanging requests
      signal: AbortSignal.timeout(10000) // 10 second timeout
    }).catch(error => {
      if (error.name === 'TimeoutError') {
        throw new Error('Request to backend API timed out');
      }
      throw error;
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Backend API error (${response.status}):`, errorText);
      
      return NextResponse.json(
        { error: `Failed to fetch solution: ${response.status} ${errorText}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in solutions API route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
