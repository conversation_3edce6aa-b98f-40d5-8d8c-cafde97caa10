'use client'

import { Suspense, useState, useEffect, useCallback } from "react"
import { SearchBar } from "@/components/SearchBar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { RecentIssues } from "@/components/recent-issues"
import { PopularSolutions } from "@/components/popular-solutions"
import { LiveIssueTracker } from "@/components/live-issue-tracker"
import { useSearchParams } from "next/navigation"
import { IssueFormDialog } from "@/components/issue-form-dialog"
import { IssueDetailsDialogClient } from "@/components/issue-details-dialog-client"
import { KnowledgeDialog } from "@/components/knowledge-dialog"
import { Button } from "@/components/ui/button" 
import { PlusCircle, BookOpen, Loader2 } from "lucide-react"
import { issueApi, SystemNameEnum, StatusEnum, CategoryEnum } from "@/lib/api"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import type { MappedIssue } from "@/types/issue-details"


type SearchResult = {
  id: number
  title: string
  description?: string
  type?: 'issue' | 'knowledge'
}

function DashboardContent() {
  const searchParams = useSearchParams()
  const [projectId, setProjectId] = useState<number>(0)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isKnowledgeDialogOpen, setIsKnowledgeDialogOpen] = useState(false)
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [selectedItem, setSelectedItem] = useState<SearchResult | null>(null)
  const [isIssueDialogOpen, setIsIssueDialogOpen] = useState(false)
  const [isLoadingIssue, setIsLoadingIssue] = useState(false)
  const [issueDetails, setIssueDetails] = useState<MappedIssue | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  // Removed unused knowledgeItem state

  // Get project ID from URL parameters
  useEffect(() => {
    const projectParam = searchParams.get("project");
    if (projectParam) {
      const id = parseInt(projectParam, 10);
      if (!isNaN(id)) {
        setProjectId(id);
      }
    }
  }, [searchParams]);

  // Handle search results update
  const handleSearchResults = useCallback((results: unknown) => {
    if (!results) {
      setSearchResults([]);
      return;
    }
    
    // Ensure results is an array
    const resultsArray = Array.isArray(results) ? results : [];
    
    // Ensure each item has the correct shape
    const validResults = resultsArray.filter((result): result is SearchResult => (
      result &&
      typeof result === 'object' &&
      'id' in result &&
      'title' in result
    ));

    // Only update if the results are actually different
    setSearchResults(prev => {
      if (prev.length !== validResults.length) return validResults;
      return prev.some((item, i) => 
        item.id !== validResults[i]?.id || 
        item.title !== validResults[i]?.title
      ) ? validResults : prev;
    });
  }, []);

  // Map API Issue to MappedInterface
  const mapToMappedIssue = (issue: any): MappedIssue => {
    // Handle both API Issue and MappedIssue types
    const now = new Date().toISOString();
    const apiIssue = issue as any; // Temporary type assertion
    
    return {
      id: apiIssue.issue_id || apiIssue.id,
      title: apiIssue.title || 'No title',
      error_code: apiIssue.error_code || apiIssue.errorCode || '',
      error_message: apiIssue.error_message || apiIssue.errorMessage || '',
      issue_type: apiIssue.issue_type,
      system_name: apiIssue.system_name || SystemNameEnum.OTHERS,
      status: apiIssue.status || StatusEnum.Open,
      impact: apiIssue.impact || 'LOW',
      frequency: apiIssue.frequency || 'RARE',
      category: apiIssue.category || CategoryEnum.Exceptions,
      description: apiIssue.description || 'No description available',
      createdBy: apiIssue.created_by?.toString() || 'Unknown',
      createdAt: apiIssue.created_at || now,
      jira_id: apiIssue.jira_id || null,
      jira_link: apiIssue.jira_link || null,
      hemants_view: apiIssue.hemants_view || null,
      solution: apiIssue.solution || { exists: false },
      // issue_type is not part of MappedIssue, so we'll omit it
    };
  };

  // Handle search result click
  const handleResultClick = useCallback(async (e: React.MouseEvent, result: SearchResult) => {
    e.preventDefault();
    e.stopPropagation();
    
    setSelectedItem(result);
    
    if (result.type === 'issue' || !result.type) { // Default to issue dialog if type not specified
      setIsLoadingIssue(true);
      try {
        // Fetch the full issue details
        const issueId = Number(result.id);
        const details = await issueApi.getIssue(issueId);
        const mappedIssue = mapToMappedIssue({
          ...details,
          // Ensure required fields are present
          issue_id: details.issue_id || issueId,
          title: details.title || result.title,
          description: details.description || result.description || 'No description available',
          status: details.status || StatusEnum.Open,
          system_name: details.system_name || 'UNKNOWN',
          created_at: details.created_at || new Date().toISOString(),
          created_by: details.created_by || 'Unknown',
          jira_id: details.jira_id || null,
          jira_link: details.jira_link || null,
          hemants_view: details.hemants_view || null,
          issue_type: details.issue_type || 'ISSUE',
        });
        setIssueDetails(mappedIssue);
        setIsIssueDialogOpen(true);
      } catch (error) {
        console.error('Error fetching issue details:', error);
        // Fallback to showing basic info if fetch fails
        const fallbackIssue = {
          issue_id: result.id,
          title: result.title,
          description: result.description || 'No description available',
          status: StatusEnum.Open,
          system_name: 'UNKNOWN',
          created_at: new Date().toISOString(),
          created_by: 'Unknown',
          jira_id: null,
          jira_link: null,
          hemants_view: null,
          issue_type: 'ISSUE',
        };
        setIssueDetails(mapToMappedIssue(fallbackIssue));
        setIsIssueDialogOpen(true);
      } finally {
        setIsLoadingIssue(false);
      }
    } else {
      // Handle knowledge base items
      setIsKnowledgeDialogOpen(true);
    }
  }, []);

  return (
    <div className="container mx-auto p-4 md:p-6 max-w-7xl">
      <div className="flex flex-col gap-4 md:gap-6">
        <div className="flex flex-col gap-2 md:flex-row md:justify-between">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome to ExTrack. Search, track, and resolve exceptions efficiently.
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="md:w-auto md:self-start" onClick={() => setIsKnowledgeDialogOpen(true)}>
              <BookOpen className="mr-2 h-4 w-4"/>New Knowledge
            </Button>
            <Button className="md:w-auto md:self-start" onClick={() => setIsDialogOpen(true)}>
              <PlusCircle className="mr-2 h-4 w-4"/>New Issue
            </Button>
          </div>
        </div>
        <KnowledgeDialog 
          open={isKnowledgeDialogOpen} 
          onOpenChange={setIsKnowledgeDialogOpen} 
          onEntryCreated={() => {
            // Refresh knowledge entries or update UI as needed
            setSearchResults([]);
            setSearchQuery('');
          }}
        />
        <IssueFormDialog 
          open={isDialogOpen} 
          onOpenChange={setIsDialogOpen} 
          onSubmit={async (data) => {
            console.log(data);
            return Promise.resolve();
          }} 
          mode="create" 
          projectId={projectId} 
        />
        <SearchBar 
          project_id={projectId} 
          type="all" 
          onSearch={setSearchQuery}
          onResults={handleSearchResults}
          initialQuery={searchQuery}
        />
{searchQuery && (
  <Card>
    <CardHeader>
      <CardTitle>Search Results</CardTitle>
      <CardDescription>
        {searchResults.length > 0 
          ? `Found ${searchResults.length} results for "${searchQuery}"`
          : "No results found"}
      </CardDescription>
    </CardHeader>
    <CardContent>
      {selectedItem && issueDetails && (
        <IssueDetailsDialogClient
          open={isIssueDialogOpen}
          onOpenChange={setIsIssueDialogOpen}
          issue = {issueDetails }
          onToggleFavorite={async () => {
            await Promise.resolve();
          }}
          isFavorite={false}
        />
      )}
      {selectedItem && (
        <KnowledgeDialog
          open={isKnowledgeDialogOpen}
          onOpenChange={setIsKnowledgeDialogOpen}
          entry={{
            title: selectedItem.title,
            content: selectedItem.description || 'No content available',
            id: selectedItem.id,
            created_by: 'Unknown',
            created_at: new Date().toISOString(),
          }}
          mode="view"
        />
      )}
    </CardContent>
  </Card>
)}

        <div className={`grid grid-cols-1 ${!searchQuery ? 'md:grid-cols-3' : 'md:grid-cols-1'} gap-4 md:gap-6`}>
          <Card className="md:col-span-2">
            <CardHeader className="pb-2">
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest issues and solutions from your projects</CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="issues">
                <TabsList className="mb-4">
                  <TabsTrigger value="issues">Recent Issues</TabsTrigger>
                  <TabsTrigger value="solutions">Popular Solutions</TabsTrigger>
                </TabsList>
                <TabsContent value="issues">
                  <RecentIssues projectId={projectId} />
                </TabsContent>
                <TabsContent value="solutions">
                  <PopularSolutions />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Live Issue Tracker</CardTitle>
              <CardDescription>Real-time status of active issues</CardDescription>
            </CardHeader>
            <CardContent>
              <LiveIssueTracker />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default function DashboardPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen"><LoadingSpinner /></div>}>
      <DashboardContent />
    </Suspense>
  )
}

