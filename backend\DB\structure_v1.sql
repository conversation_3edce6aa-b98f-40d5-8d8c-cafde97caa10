-- Create and use the central database
CREATE DATABASE IF NOT EXISTS central_db;
USE central_db;

-- Users table
CREATE TABLE users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Admins table
CREATE TABLE admins (
    admin_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Projects table
CREATE TABLE projects (
    project_id INT AUTO_INCREMENT PRIMARY KEY,
    project_name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    icon_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Project Access table
CREATE TABLE project_access (
    access_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    project_id INT NOT NULL,
    access_level ENUM('READ', 'WRITE', 'ADMIN') NOT NULL DEFAULT 'READ',
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (project_id) REFERENCES projects(project_id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_project (user_id, project_id)
);

-- Issues table (updated)
CREATE TABLE issues (
    issue_id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    project_id INT,
    category VARCHAR(50),
    title VARCHAR(255),
    description TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    error_code VARCHAR(50),
    error_message VARCHAR(255),
    issue_type VARCHAR(100),
    status ENUM('Open', 'In Progress', 'Resolved', 'Closed') DEFAULT 'Open',
    impact ENUM('Low', 'Medium', 'High', 'Critical'),
    frequency ENUM('Rare', 'Occasional', 'Frequent', 'Always'),
    jira_id VARCHAR(50),
    jira_link VARCHAR(255),
    hemants_view TEXT,
    system_name ENUM('OMS', 'WMS', 'AUTOMATION', 'OTHERS'),
    reviewed_by INT,
    reviewed_at TIMESTAMP,
    KEY (project_id),
    KEY (created_by),
    KEY (reviewed_by),
    FOREIGN KEY (project_id) REFERENCES projects(project_id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Solutions table
CREATE TABLE solutions (
    solution_id INT AUTO_INCREMENT PRIMARY KEY,
    issue_id INT NOT NULL,
    category VARCHAR(50) NOT NULL,
    provided_by INT NOT NULL,
    solution_text TEXT NOT NULL,
    upvotes INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (issue_id) REFERENCES issues(issue_id) ON DELETE CASCADE,
    FOREIGN KEY (provided_by) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Solution history table
CREATE TABLE solution_history (
    history_id INT AUTO_INCREMENT PRIMARY KEY,
    solution_id INT NOT NULL,
    previous_text TEXT NOT NULL,
    updated_by INT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (solution_id) REFERENCES solutions(solution_id) ON DELETE CASCADE,
    FOREIGN KEY (updated_by) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Issue files table
CREATE TABLE issue_files (
    file_id INT AUTO_INCREMENT PRIMARY KEY,
    issue_id INT NOT NULL,
    file_url VARCHAR(500) NOT NULL,
    uploaded_by INT NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (issue_id) REFERENCES issues(issue_id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(user_id) ON DELETE CASCADE
);

CREATE TABLE knowledge_base (
    kb_id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    uploaded_by INT NOT NULL,
    file_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    solution_id INT,
    FOREIGN KEY (project_id) REFERENCES projects(project_id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (solution_id) REFERENCES solutions(solution_id) ON DELETE SET NULL
);
