from sqlalchemy.orm import Session, joinedload
from backend import models

def get_all_projects(db: Session):
    """
    Retrieve all projects from the database.

    Args:
        db (Session): Database session

    Returns:
        List[models.Project]: List of all projects
    """
    return db.query(models.Project).all()

def get_projects_with_user_counts(db: Session):
    """
    Retrieve all projects with user counts.

    Args:
        db (Session): Database session

    Returns:
        List[Tuple]: List of projects with user counts
    """
    from sqlalchemy import func
    
    return db.query(
        models.Project,
        func.count(models.UserProjectRole.user_id).label("user_count")
    ).outerjoin(
        models.UserProjectRole,
        models.Project.project_id == models.UserProjectRole.project_id
    ).group_by(
        models.Project.project_id
    ).all()

def get_project(db: Session, project_id: int):
    """
    Retrieve a specific project by ID.

    Args:
        db (Session): Database session
        project_id (int): ID of the project to retrieve

    Returns:
        models.Project: The requested project or None if not found
    """
    return db.query(models.Project).filter(models.Project.project_id == project_id).first()

def create_project(db: Session, project_name: str, description: str, icon_name: str):
    """
    Create a new project.

    Args:
        db (Session): Database session
        project_name (str): Name of the project
        description (str): Description of the project
        icon_name (str): Icon name for the project

    Returns:
        models.Project: The newly created project
    """
    db_project = models.Project(
        project_name=project_name,
        description=description,
        icon_name=icon_name
    )
    db.add(db_project)
    db.commit()
    db.refresh(db_project)
    return db_project

def update_project(db: Session, project_id: int, project_data: dict):
    """
    Update an existing project.

    Args:
        db (Session): Database session
        project_id (int): ID of the project to update
        project_data (dict): Dictionary containing fields to update

    Returns:
        models.Project: The updated project or None if not found
    """
    db_project = get_project(db, project_id)
    if db_project:
        for key, value in project_data.items():
            setattr(db_project, key, value)
        db.commit()
        db.refresh(db_project)
    return db_project

def delete_project(db: Session, project_id: int):
    """
    Delete a project.

    Args:
        db (Session): Database session
        project_id (int): ID of the project to delete

    Returns:
        bool: True if project was deleted, False otherwise
    """
    db_project = get_project(db, project_id)
    if db_project:
        db.delete(db_project)
        db.commit()
        return True
    return False

def get_all_issues(db: Session):
    """
    Retrieve all issues from the database.

    Args:
        db (Session): Database session

    Returns:
        List[models.Issue]: List of all issues
    """
    return db.query(models.Issue).options(joinedload(models.Issue.solutions)).all()
        