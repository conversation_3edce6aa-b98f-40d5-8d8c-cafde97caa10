from fastapi import APIRouter, Depends, HTTPException, status, Response
from sqlalchemy.orm import Session
from typing import List
from backend.database import get_db
from backend.dependencies import get_current_user
from backend.models import User, Issue, UserFavorite
from pydantic import BaseModel

router = APIRouter(
    prefix="/favorites",
    tags=["favorites"],
    responses={404: {"description": "Not found"}},
)

class FavoriteResponse(BaseModel):
    issue_id: int
    
    class Config:
        from_attributes = True

class FavoriteCreate(BaseModel):
    issue_id: int

@router.get("/", response_model=List[FavoriteResponse])
async def get_user_favorites(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get all favorites for the current user
    """
    favorites = db.query(UserFavorite).filter(
        UserFavorite.user_id == current_user.user_id
    ).all()
    
    return favorites

@router.post("/", response_model=FavoriteResponse)
async def add_favorite(
    favorite: FavoriteCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Add an issue to user's favorites
    """
    # Check if the issue exists
    issue = db.query(Issue).filter(Issue.issue_id == favorite.issue_id).first()
    if not issue:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Issue with ID {favorite.issue_id} not found"
        )
    
    # Check if already favorited
    existing_favorite = db.query(UserFavorite).filter(
        UserFavorite.user_id == current_user.user_id,
        UserFavorite.issue_id == favorite.issue_id
    ).first()
    
    if existing_favorite:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Issue with ID {favorite.issue_id} is already in favorites"
        )
    
    # Create new favorite
    new_favorite = UserFavorite(
        user_id=current_user.user_id,
        issue_id=favorite.issue_id
    )
    
    db.add(new_favorite)
    db.commit()
    db.refresh(new_favorite)
    
    return new_favorite

@router.delete("/{issue_id}", status_code=status.HTTP_204_NO_CONTENT)
async def remove_favorite(
    issue_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Remove an issue from user's favorites
    """
    favorite = db.query(UserFavorite).filter(
        UserFavorite.user_id == current_user.user_id,
        UserFavorite.issue_id == issue_id
    ).first()
    
    # If the favorite doesn't exist, we can consider this a success
    # since the end result is the same - the favorite is not in the database
    if not favorite:
        # Instead of raising an error, just return success
        return Response(status_code=status.HTTP_204_NO_CONTENT)
    
    db.delete(favorite)
    db.commit()
    
    return None