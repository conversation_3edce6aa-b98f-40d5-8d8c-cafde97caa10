from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from ..database import get_db
from ..models import User, models
from ..schemas import UserUpdate, UserResponse
from ..dependencies import get_current_user

router = APIRouter()

@router.get("/users", response_model=List[UserResponse])
async def get_users(db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """Get all users"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized")
    users = db.query(User).all()
    return users

@router.put("/users/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user_update: UserUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update a user's information"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized")

    db_user = db.query(User).filter(User.user_id == user_id).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="User not found")

    # Update user fields if provided
    if user_update.name is not None:
        db_user.name = user_update.name
    if user_update.email is not None:
        db_user.email = user_update.email
    if user_update.role is not None:
        db_user.role = user_update.role
        db_user.is_admin = (user_update.role.lower() == "admin")
    if user_update.status is not None:
        db_user.status = user_update.status

    # --- Project assignment logic ---
    if user_update.projects is not None:
        # Remove all existing project assignments for this user
        db.query(models.UserProjectRole).filter(models.UserProjectRole.user_id == user_id).delete()
        # Add new assignments
        for project_code in user_update.projects:
            # Find project by code or name
            project = db.query(models.Project).filter((models.Project.project_name == project_code) | (models.Project.icon_name == project_code)).first()
            if project:
                new_role = models.UserProjectRole(
                    user_id=user_id,
                    project_id=project.project_id,
                    role="ADMIN" if db_user.is_admin else "USER"
                )
                db.add(new_role)
    # --- End project assignment logic ---

    try:
        db.commit()
        db.refresh(db_user)
        return db_user
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/users/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete a user"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized")

    db_user = db.query(User).filter(User.user_id == user_id).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="User not found")

    try:
        db.delete(db_user)
        db.commit()
        return None
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))