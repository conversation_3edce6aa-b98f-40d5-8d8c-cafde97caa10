from pydantic import BaseModel, EmailStr
from datetime import datetime
from typing import Optional, List
from .models import RoleEnum

class UserBase(BaseModel):
    name: str
    email: EmailStr
    password_hash: str

class UserCreate(UserBase):
    pass

class UserResponse(BaseModel):
    user_id: int
    name: str
    email: str
    created_at: datetime
    is_admin: bool = False

    class Config:
        from_attributes = True

class UserLogin(BaseModel):
    name: str
    email: EmailStr
    password: str
    role: str = "user"

class UserProfileResponse(BaseModel):
    id: str
    name: str
    email: str
    is_admin: bool = False

class UserUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    role: Optional[str] = None
    status: Optional[str] = None
    projects: Optional[List[str]] = None