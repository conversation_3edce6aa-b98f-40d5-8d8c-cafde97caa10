from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi.responses import JSONResponse
from jose import jwt
from backend.config import SECRET_KEY, ALGORITHM, ACCESS_TOKEN_EXPIRE_MINUTES
from backend.models import User

def format_solution_response(solution, provider_name: str = None) -> dict:
    """Format a solution response consistently."""
    return {
        "solution_id": solution.solution_id,
        "issue_id": solution.issue_id,
        "category": solution.category,
        "provided_by": solution.provided_by,
        "solution_text": solution.solution_text,
        "upvotes": solution.upvotes,
        "created_at": solution.created_at,
        "provided_by_name": provider_name or (solution.provider.name if solution.provider else None)
    }

def create_token_response(user: User, expires_delta: Optional[timedelta] = None) -> JSONResponse:
    """Create a standardized token response with cookie."""
    is_admin = hasattr(user, 'admin') and user.admin is not None
    
    access_token = jwt.encode(
        {
            "sub": user.email,
            "is_admin": is_admin,
            "user_id": user.user_id,
            "exp": datetime.utcnow() + (expires_delta or timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES))
        },
        SECRET_KEY,
        algorithm=ALGORITHM
    )

    response_data = {
        "access_token": access_token,
        "token_type": "bearer",
        "is_admin": is_admin,
        "user_id": user.user_id,
        "name": user.name,
        "message": "Successfully logged in"
    }

    response = JSONResponse(content=response_data)
    response.set_cookie(
        key="access_token",
        value=f"Bearer {access_token}",
        httponly=True,
        secure=True,
        samesite="lax",
        max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        path="/",
    )
    return response

def get_db_error_response(db: Any, error: Exception) -> JSONResponse:
    """Handle database errors consistently."""
    try:
        db.rollback()
    except:
        pass
    return JSONResponse(
        status_code=400,
        content={"detail": str(error)}
    )
