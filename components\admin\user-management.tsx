"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { MoreHorizontal, Search, UserPlus } from "lucide-react"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { Loader } from "@/components/ui/loader"
import { usersApi } from "@/lib/api/users"
import { User } from "@/lib/api"

interface UserWithId extends Omit<User, 'role'> {
  id: number
  projects: string[]
  name: string
  email: string
  created_at: string
  is_admin: boolean
  status: 'Active' | 'Inactive'
  role?: string
}

export function UserManagement() {
  const { toast } = useToast()
  const [users, setUsers] = useState<UserWithId[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false)
  const [newUser, setNewUser] = useState({
    name: "",
    email: "",
    role: "User",
    projects: [],
  })

  useEffect(() => {
    fetchUsers()
  }, [])

  const fetchUsers = async () => {
    try {
      const response = await usersApi.getAllUsers()
      const usersWithId = (response.items || []).map(user => ({
        ...user,
        id: user.user_id,
        projects: user.projects || []
      }))
      setUsers(usersWithId)
    } catch (error) {
      console.error('Error fetching users:', error)
      toast({
        title: "Error",
        description: "Failed to load users",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const filteredUsers = users.filter(
    (user) =>
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const validateForm = (data: { name: string; email: string; role: string }) => {
    const errors: string[] = []
    if (!data.name.trim()) errors.push('Name is required')
    if (!data.email.trim()) errors.push('Email is required')
    if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(data.email)) {
      errors.push('Invalid email address')
    }
    if (!data.role) errors.push('Role is required')
    return errors
  }

  const handleAddUser = async () => {
    try {
      const errors = validateForm(newUser)
      if (errors.length > 0) {
        errors.forEach(error => {
          toast({
            title: "Validation Error",
            description: error,
            variant: "destructive",
          })
        })
        return
      }

      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newUser.name,
          email: newUser.email,
          role: newUser.role,
          projects: newUser.projects,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to create user')
      }

      await fetchUsers() // Refresh the users list
      setIsAddUserDialogOpen(false)
      setNewUser({
        name: "",
        email: "",
        role: "Employee",
        projects: [],
      })

      toast({
        title: "Success",
        description: "User has been created successfully",
      })
    } catch (error) {
      console.error('Error creating user:', error)
      toast({
        title: "Error",
        description: "Failed to create user",
        variant: "destructive",
      })
    }
  }

  const handleEditUser = async (userId: number, userData: Partial<User>) => {
    try {
      const errors = validateForm({
        name: userData.name || '',
        email: userData.email || '',
        role: userData.role || ''
      })
      if (errors.length > 0) {
        errors.forEach(error => {
          toast({
            title: "Validation Error",
            description: error,
            variant: "destructive",
          })
        })
        return
      }

      await usersApi.updateUser(userId, {
        ...userData,
        is_admin: userData.role === 'Admin'
      })

      await fetchUsers() // Refresh the users list
      toast({
        title: "Success",
        description: "User has been updated successfully",
      })
    } catch (error) {
      console.error('Error updating user:', error)
      toast({
        title: "Error",
        description: "Failed to update user",
        variant: "destructive",
      })
    }
  }

  const handleDeleteUser = async (userId: number) => {
    try {
      await usersApi.deleteUser(userId)
      await fetchUsers() // Refresh the users list
      toast({
        title: "Success",
        description: "User has been deleted successfully",
      })
    } catch (error) {
      console.error('Error deleting user:', error)
      toast({
        title: "Error",
        description: "Failed to delete user",
        variant: "destructive",
      })
    }
  }

  const handleChangeRole = async (userId: number, newRole: string) => {
    try {
      await usersApi.updateUserRole(userId, newRole)
      await fetchUsers() // Refresh the users list
      toast({
        title: "Success",
        description: `User role has been updated to ${newRole}`,
      })
    } catch (error) {
      console.error('Error updating user role:', error)
      toast({
        title: "Error",
        description: "Failed to update user role",
        variant: "destructive",
      })
    }
  }

  const handleChangeStatus = async (userId: number, newStatus: string) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (!response.ok) {
        throw new Error('Failed to update user status')
      }

      await fetchUsers() // Refresh the users list
      toast({
        title: "Success",
        description: `User status has been updated to ${newStatus}`,
      })
    } catch (error) {
      console.error('Error updating user status:', error)
      toast({
        title: "Error",
        description: "Failed to update user status",
        variant: "destructive",
      })
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  if (loading) {
    return <div className="flex justify-center items-center h-full"><Loader /></div>
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative w-64">
          <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search users..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Dialog open={isAddUserDialogOpen} onOpenChange={setIsAddUserDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <UserPlus className="mr-2 h-4 w-4" /> Add User
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New User</DialogTitle>
              <DialogDescription>
                Add a new user to the system. They will receive an email invitation.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  placeholder="John Doe"
                  value={newUser.name}
                  onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={newUser.email}
                  onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Select 
                  value={newUser.role} 
                  onValueChange={(value) => setNewUser({ ...newUser, role: value })}
                >
                  <SelectTrigger id="role">
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Admin">Admin</SelectItem>
                    <SelectItem value="User">User</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="projects">Projects</Label>
                <Select>
                  <SelectTrigger id="projects">
                    <SelectValue placeholder="Select projects" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="oms">Order Management System</SelectItem>
                    <SelectItem value="wms">Warehouse Management System</SelectItem>
                    <SelectItem value="automation">Automation Platform</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Multiple project selection will be implemented in the final version.
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddUserDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddUser}>Add User</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Projects</TableHead>
              <TableHead className="w-[80px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredUsers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4 text-muted-foreground">
                  No users found matching your search.
                </TableCell>
              </TableRow>
            ) : (
              filteredUsers.map((user) => (
                <TableRow key={user.user_id}>
                  <TableCell className="font-medium">{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <Badge 
                      variant={user.role === 'Admin' ? "default" : "outline"}
                      className={user.role === 'Admin' ? "bg-purple-500 hover:bg-purple-600" : "bg-gray-500 hover:bg-gray-600"}
                    >
                      {user.role}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={user.status === "Active" ? "default" : "secondary"}
                      className={user.status === "Active" ? "bg-green-500 hover:bg-green-600" : ""}
                    >
                      {user.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {user.projects?.map((project) => (
                        <Badge key={project} variant="outline" className="text-xs">
                          {project}
                        </Badge>
                      ))}                      
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Actions</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => handleChangeRole(user.user_id, user.role === 'Admin' ? 'User' : 'Admin')}
                        >
                          {user.role === 'Admin' ? 'Demote to User' : 'Promote to Admin'}
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleChangeStatus(user.user_id, user.status === "Active" ? "Inactive" : "Active")}
                        >
                          {user.status === "Active" ? "Deactivate User" : "Activate User"}
                        </DropdownMenuItem>
                        <DropdownMenuItem onSelect={() => handleEditUser(user.user_id, { name: user.name, email: user.email })}>Edit User</DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-destructive"
                          onSelect={() => {
                            if (window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                              handleDeleteUser(user.user_id)
                            }
                          }}
                        >
                          Delete User
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}

