"use client"

import * as React from "react"
import { useState, useMemo } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown, ChevronUp, Mail, Shield, User as UserIcon, Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { usersApi } from "@/lib/api/users"
import { User } from "@/lib/api/types"
import { useUser } from "@/contexts/user-context"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"

interface ExtendedUser extends User {
  projects?: string[],
  role?: string,
  status: "Active" | "Inactive"
}

interface UsersListProps {
  filter: "all" | "active" | "inactive" | "admin"
}

export function UsersList({ filter }: UsersListProps) {
  const [expandedUser, setExpandedUser] = useState<number | null>(null)
  const [users, setUsers] = useState<ExtendedUser[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [updatingStatus, setUpdatingStatus] = useState<Record<number, boolean>>({})
  const { toast } = useToast()
  const { user: currentUser } = useUser()
  
  if (!currentUser?.is_admin) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">You don't have permission to view this page.</p>
      </div>
    )
  }

  // Enhanced fetch users with better error handling and debugging
  React.useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      setError(null);
      
      try {
        console.group('🔍 Fetching Users');
        console.log('⏳ Starting API call to fetch users...');
        
        const response = await usersApi.getAllUsers();
        console.log('✅ API Response:', response);
        
        if (!response) {
          throw new Error('No response received from the server');
        }
        
        // Debug: Log the exact response structure
        console.log('📊 Response Structure:', {
          isArray: Array.isArray(response),
          hasData: !!(response as any)?.data,
          hasItems: !!(response as any)?.items,
          keys: Object.keys(response as object)
        });
        
        // Extract users array from different possible response structures
        let usersArray = [];
        if (Array.isArray(response)) {
          usersArray = response;
        } else if (response && typeof response === 'object') {
          usersArray = Array.isArray((response as any)?.data) ? (response as any).data : 
                    Array.isArray((response as any)?.items) ? (response as any).items : [];
        }
        
        console.log('👥Extracted Users:', usersArray);
        const usersData = usersArray
          .filter((user: Partial<ExtendedUser>): user is ExtendedUser => 
            Boolean(user && (user.user_id || (user as any).id))
          )
          .map((user: Partial<ExtendedUser>): ExtendedUser => {
            const userId = user.user_id || (user as any).id;
            const userStatus = user.status || 'Active';
            const userRole = user.role || (user.is_admin ? 'Admin' : 'User');
            const userProjects = Array.isArray(user.projects) ? user.projects : [];
            
            // Debug each user
            console.log('👤 Processed User:', {
              userId,
              name: user.name || 'Unnamed',
              status: userStatus,
              role: userRole,
              projects: userProjects
            });
            
            return {
              ...user,
              user_id: userId, // Ensure user_id is always set
              status: userStatus as "Active" | "Inactive",
              role: userRole,
              projects: userProjects,
              name: user.name || 'Unnamed', // Ensure name is always a string
              email: user.email || '', // Ensure email is always a string
              is_admin: user.is_admin || false, // Ensure is_admin is always a boolean
              created_at: user.created_at || new Date().toISOString() // Ensure created_at is always a string
            };
          });
        
        console.log('👥 Final Users Data:', usersData);
        setUsers(usersData);
        
        if (usersData.length === 0) {
          console.warn(' shawty owe me something better pay her toll 👥 No users found in the response');
        }
        
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load users';
        console.error(' Error fetching users:', errorMessage, err);
        
        // More detailed error handling
        if (err instanceof TypeError) {
          console.error('TypeError details:', {
            message: err.message,
            stack: err.stack,
            name: err.name
          });
        }
        
        setError(errorMessage);
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      } finally {
        console.groupEnd();
        setLoading(false);
      }
    };
    fetchUsers();
  }, [filter, toast]); // Refetch when filter changes

  // Filter users based on the selected filter with proper typing
  const filteredUsers = useMemo<ExtendedUser[]>(() => {
    if (!Array.isArray(users)) {
      console.warn('Users is not an array:', users);
      return [];
    }

    return users.filter((user: ExtendedUser) => {
      if (!user || !user.user_id) {
        console.warn('Skipping invalid user:', user);
        return false;
      }

      const userStatus = (user.status || 'Active').toLowerCase();
      const userRole = user.role || (user.is_admin ? 'Admin' : 'User');

      switch (filter) {
        case 'active':
          return userStatus === 'active';
        case 'inactive':
          return userStatus === 'inactive';
        case 'admin':
          return userRole === 'Admin' || user.is_admin === true;
        case 'all':
        default:
          return true;
      }
    });
  }, [users, filter]);

  const toggleExpand = (user_id: number) => {
    setExpandedUser(expandedUser === user_id ? null : user_id);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading users...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-4 text-red-500">
        Error loading users: {error}
      </div>
    );
  }

  if (filteredUsers.length === 0) {
    return (
      <div className="text-center p-8 text-muted-foreground">
        No users found matching the selected filter.
      </div>
    );
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never'
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      })
    } catch (e) {
      return 'Invalid date'
    }
  }

  // Toggle user status between Active and Inactive
  const toggleUserStatus = async (userId: number, currentStatus: 'Active' | 'Inactive' = 'Active') => {
    if (!currentUser?.is_admin) {
      toast({
        title: "Access Denied",
        description: "Only admins can update user status",
        variant: "destructive",
      });
      return;
    }

    const newStatus = currentStatus === 'Active' ? 'Inactive' : 'Active';
    
    try {
      setUpdatingStatus(prev => ({ ...prev, [userId]: true }));
      
      // Update the user's status in the backend
      await usersApi.updateUser(userId, { status: newStatus });
      
      // Update the local state to reflect the change
      setUsers(users.map(user => 
        user.user_id === userId 
          ? { ...user, status: newStatus } 
          : user
      ));
      
      toast({
        title: "Success",
        description: `User status updated to ${newStatus}`,
      });
    } catch (err) {
      console.error("Error updating user status:", err);
      toast({
        title: "Error",
        description: "Failed to update user status",
        variant: "destructive",
      });
    } finally {
      setUpdatingStatus(prev => ({ ...prev, [userId]: false }));
    }
  }

  const handleEditUser = (userId: number) => {
    console.log(`Edit user with ID: ${userId}`);
    // TODO: Implement edit user functionality (e.g., open a dialog)
    toast({
      title: "Edit User",
      description: `Edit functionality for user ${userId} will be implemented here`,
    });
  }

  const handleResetPassword = (userId: number) => {
    console.log(`Reset password for user with ID: ${userId}`);
    // TODO: Implement reset password functionality (call API)
    toast({
      title: "Reset Password",
      description: `Password reset functionality for user ${userId} will be implemented here`,
    });
  }

  const handleManageProjects = (userId: number) => {
    console.log(`Manage projects for user with ID: ${userId}`);
    // TODO: Implement manage projects functionality (e.g., open a dialog)
    toast({
      title: "Manage Projects",
      description: `Project management for user ${userId} will be implemented here`,
    });
  }

  const handleDeactivateUser = async (userId: number) => {
    if (!confirm('Are you sure you want to deactivate this user?')) {
      return;
    }
    
    console.log(`Deactivate user with ID: ${userId}`);
    try {
      // TODO: Uncomment and implement the actual API call
      // await usersApi.deactivateUser(userId);
      
      toast({
        title: "User Deactivated",
        description: `User with ID ${userId} has been deactivated.`,
      });
      
      // Refresh the user list after deactivation
      setUsers(users.filter(user => user.user_id !== userId));
    } catch (error) {
      console.error("Error deactivating user:", error);
      toast({
        title: "Error",
        description: "Failed to deactivate user.",
        variant: "destructive",
      });
    }
  }

  return (
    <div className="space-y-4">
      {/* Loading state */}
      {loading && (
        <div className="flex justify-center items-center p-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading users...</span>  
        </div>
      )}

      {/* Error state */}
      {error && !loading && (
        <div className="text-center p-4 text-red-500">
          Error loading users: {error}
        </div>
      )}

      {/* Empty state */}
      {!loading && !error && filteredUsers.length === 0 && (
        <div className="text-center p-8 text-muted-foreground">
          No users found matching the selected filter.
        </div>
      )}
     {/* User list */}
      {!loading && !error && filteredUsers.length > 0 && (
        filteredUsers.map((user) => (
          <Collapsible key={user.user_id} open={expandedUser === user.user_id} onOpenChange={() => toggleExpand(user.user_id)}>
            <Card className="overflow-hidden hover:shadow-sm transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium">{user.name}</h3>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-2">
                          {currentUser?.is_admin ? (
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                toggleUserStatus(user.user_id, user.status as "Active" | "Inactive")
                              }}
                              disabled={updatingStatus[user.user_id]}
                              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 ${user.status === 'Active' ? 'bg-green-500' : 'bg-gray-300'}`}
                            >
                              <span
                                className={`${user.status === 'Active' ? 'translate-x-6' : 'translate-x-1'} inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                              />
                              {updatingStatus[user.user_id] && (
                                <Loader2 className="absolute left-1/2 h-3 w-3 -translate-x-1/2 animate-spin text-white" />
                              )}
                            </button>
                          ) : (
                            <Badge
                              className={
                                user.status === "Active"
                                  ? "bg-green-500 hover:bg-green-600"
                                  : "bg-gray-500 hover:bg-gray-600"
                              }
                            >
                              {user.status}
                            </Badge>
                          )}
                        </div>
                        <Badge 
                          className={user.role === 'Admin' ? "bg-purple-500 hover:bg-purple-600" : "bg-gray-500 hover:bg-gray-600"}
                        >
                          {user.role}
                        </Badge>
                      </div>
                    </div>
                    <div className="mt-1 flex flex-wrap gap-2 text-xs text-muted-foreground">
                      <span className="inline-flex items-center gap-1">
                        <Mail className="h-3 w-3" /> {user.email}
                      </span>
                      <span className="inline-flex items-center gap-1">
                        <Shield className="h-3 w-3" /> {user.role}
                      </span>
                      {user.projects && user.projects.length > 0 && (
                        <span className="inline-flex items-center gap-1">
                          <UserIcon className="h-3 w-3" /> {user.projects.join(", ")}
                        </span>
                      )}
                      <span className="ml-auto">Last active: {formatDate(user.created_at)}</span>
                    </div>
                  </div>
                  <CollapsibleTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-8 w-8 p-0" 
                      aria-label={expandedUser === user.user_id ? "Collapse user details" : "Expand user details"}
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleExpand(user.user_id);
                      }}
                    >
                      {expandedUser === user.user_id ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </Button>
                  </CollapsibleTrigger>
                </div>
              </CardContent>
              <CollapsibleContent>
                <CardContent className="border-t px-4 py-3">
                  <div className="flex gap-2">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button size="sm" variant="outline" onClick={() => handleEditUser(user.user_id)} aria-label="Edit user">
                            Edit
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Edit user details</TooltipContent>
                      </Tooltip>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button size="sm" variant="outline" onClick={() => handleResetPassword(user.user_id)} aria-label="Reset password">
                            Reset Password
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Reset user password</TooltipContent>
                      </Tooltip>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button size="sm" variant="outline" onClick={() => handleManageProjects(user.user_id)} aria-label="Manage projects">
                            Manage Projects
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Manage user projects</TooltipContent>
                      </Tooltip>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button size="sm" variant="destructive" onClick={() => handleDeactivateUser(user.user_id)} aria-label="Deactivate user">
                            Deactivate
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Deactivate user account</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Card>
          </Collapsible>
        ))
      )}
    </div>
  );
};

export default UsersList;    