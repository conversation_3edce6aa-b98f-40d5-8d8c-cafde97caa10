"use client"

import { useState, useCallback } from "react"
import { useToast } from "@/components/ui/use-toast"
import { IssueDetailsDialog, type MappedIssue } from "./issue-details-dialog"

interface IssueDetailsDialogClientProps {
  issue: MappedIssue | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onToggleFavorite: (issueId: number) => Promise<void>
  isFavorite: boolean
}

export function IssueDetailsDialogClient({
  issue ,
  open,
  onOpenChange,
  onToggleFavorite,
  isFavorite,
}: IssueDetailsDialogClientProps) {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  const handleToggleFavorite = useCallback(async () => {
    if (!issue) return
    
    try {
      setIsLoading(true)
      await onToggleFavorite(issue.id)
      toast({
        title: isFavorite ? "Removed from favorites" : "Added to favorites",
        variant: "default",
        duration: 2000,
      })
    } catch (error) {
      console.error('Error toggling favorite:', error)
      toast({
        title: "Failed to update favorites",
        variant: "destructive",
        duration: 2000,
      })
    } finally {
      setIsLoading(false)
    }
  }, [issue, onToggleFavorite, isFavorite, toast])

  return (
    <IssueDetailsDialog
      open={open}
      onOpenChange={onOpenChange}
      issue={issue}
      onToggleFavorite={handleToggleFavorite}
      isFavorite={isFavorite}
      isLoading={isLoading}
    />
  )
}
