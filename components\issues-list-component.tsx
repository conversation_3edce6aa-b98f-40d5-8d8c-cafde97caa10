import { useState, useEffect, useMemo } from "react";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { StatusBadge } from "@/components/ui/status-badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Star, AlertCircle, Calendar, LayoutGrid, LayoutList, Search } from "lucide-react";
import { favoritesApi, ApiError } from "@/lib/api";
import { useToast } from "@/components/ui/use-toast";
import { CategoryEnum, StatusEnum, SystemNameEnum } from "@/lib/api";
import { IssueDetailsDialog } from "@/components/issue-details-dialog";
import { useRouter } from "next/navigation";
import { getStatusColor, getImpactColor, getFrequencyInfo, getSystemInfo, formatDate, PendingOperationsState, FavoriteState, getStatusInfo } from "@/lib/utils/issue-utils";

// Add TypeScript declaration for window.__pendingTimeouts
declare global {
  interface Window {
    __pendingTimeouts?: {
      [key: string]: NodeJS.Timeout;
    };
  }
}

// Define types for the component props
interface IssuesListMappedIssue extends Omit<MappedIssue, 'comments'> {
  comments?: number;
}

interface IssuesListProps {
  filter: string;
  category: string;
  issues: IssuesListMappedIssue[];
  onRefresh: () => Promise<void>;
}

// Reuse the MappedIssue interface from the parent component
export interface MappedIssue {
  id: number;
  title: string;
  error_code?: string;
  error_message?: string;
  system_name: SystemNameEnum;
  type?: string;
  status: StatusEnum;
  impact?: string;
  frequency?: string;
  category?: CategoryEnum;
  description: string;
  createdBy: string;
  createdAt: string;
  jira_id?: string | null;
  jira_link?: string | null;
  hemants_view?: string | null;
  solution: {
    exists: boolean;
    content?: string;
    verified?: boolean;
    author?: string;
    createdAt?: string;
  };
}

// Types are now imported from @/lib/utils/issue-utils

export function IssuesList({ filter, category, issues, onRefresh }: IssuesListProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [favorites, setFavorites] = useState<FavoriteState>({});
  const [expandedIssue, setExpandedIssue] = useState<number | null>(null);
  const [isLoadingFavorites, setIsLoadingFavorites] = useState<boolean>(true);
  const [viewMode, setViewMode] = useState<"card">("card");
  const [selectedIssue, setSelectedIssue] = useState<MappedIssue | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Filter and sort issues based on search query and selected filter - memoized to prevent recalculation on every render
  const filteredIssues = useMemo(() => {
    console.log("Filtering issues with filter:", filter, "favorites state:", favorites);

    return issues
      .filter(issue => {
        // First apply search filter
        if (searchQuery) {
          const searchLower = searchQuery.toLowerCase();
          const matchesSearch = 
            issue.title.toLowerCase().includes(searchLower) ||
            issue.description.toLowerCase().includes(searchLower) ||
            issue.error_code?.toLowerCase().includes(searchLower) ||
            issue.error_message?.toLowerCase().includes(searchLower);

          if (!matchesSearch) return false;
        }

        // Then apply favorites filter
        if (filter === "favorites") {
          if (!favorites || typeof favorites !== 'object' || !favorites[issue.id]) {
            return false;
          }
          return true;
        }

        // Finally apply category filter
        if (category !== "all" && issue?.category) {
          const normalizedCategory = typeof category === 'string' ? category.replace(/-/g, '_').toUpperCase() : '';
          const issueCategory = issue.category && typeof issue.category === 'string' 
            ? issue.category.toUpperCase() 
            : '';

          // Special handling for ignored-exception to match different possible enum values
          if (category.toLowerCase() === "ignored-exception" || 
              (typeof category === 'string' && category.toLowerCase().includes('ignored'))) {
            // Check for all possible variations of ignored exceptions
            return (issueCategory && (
              issueCategory.includes('IGNORED') || 
              issueCategory.includes('EXCEPTION') ||
              issueCategory === 'IGNORED_EXCEPTION'
            )) || false;
          }

          if (normalizedCategory && issueCategory) {
            if (!issueCategory.includes(normalizedCategory) &&
                !normalizedCategory.includes(issueCategory.replace('_', ''))) {
              return false;
            }
          } else {
            return false;
          }
        }
        return true;
      })
      // Sort by createdAt in descending order (newest first)
      .sort((a, b) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });
  }, [issues, filter, category, favorites, searchQuery]);

  // Refresh favorites when filter changes to "favorites"
  useEffect(() => {
    if (filter === "favorites") {
      const refreshFavorites = async () => {
        try {
          const response = await favoritesApi.getUserFavorites();
          const updatedFavorites: FavoriteState = {};
          response?.items?.forEach((favorite) => {
            if (favorite?.issue_id) {
              updatedFavorites[favorite.issue_id] = true;
            }
          });
          setFavorites(updatedFavorites);
          localStorage.setItem("userFavorites", JSON.stringify(updatedFavorites));
        } catch (error) {
          console.error("Error refreshing favorites:", error);
        }
      };
      refreshFavorites();
    }
  }, [filter]);

  // Fetch user favorites on component mount
  useEffect(() => {
    const fetchFavorites = async () => {
      try {
        const response = await favoritesApi.getUserFavorites();
        const favoritesMap: FavoriteState = {};
        
        response?.items?.forEach((favorite) => {
          if (favorite?.issue_id) {
            favoritesMap[favorite.issue_id] = true;
          }
        });
        
        setFavorites(favoritesMap);
        localStorage.setItem("userFavorites", JSON.stringify(favoritesMap));
      } catch (error) {
        console.error("Error fetching favorites:", error);
        // Fallback to localStorage
        const storedFavorites = localStorage.getItem("userFavorites");
        if (storedFavorites) {
          setFavorites(JSON.parse(storedFavorites));
        }
      } finally {
        setIsLoadingFavorites(false);
      }
    };

    fetchFavorites();
  }, []);

  // Toggle favorite status for an issue
  const toggleFavorite = async (issueId: number, retryCount: number = 0) => {
    try {
      // Create a new favorites object to avoid direct state mutation
      const newFavorites = { ...favorites };
      const isFavorite = !newFavorites[issueId];

      // Update local state immediately for responsive UI
      newFavorites[issueId] = isFavorite;
      setFavorites(newFavorites);

      // Also update localStorage as a backup
      localStorage.setItem("userFavorites", JSON.stringify(newFavorites));

      // Check if we're in development mode and need to use mock data
      const isDev = process.env.NODE_ENV === 'development';
      const useMockData = isDev && (process.env['NEXT_PUBLIC_USE_MOCK_DATA'] === 'true' || !process.env['NEXT_PUBLIC_API_URL']);

      if (!useMockData) {

        try {
          if (isFavorite) {
            console.log(`Adding issue ${issueId} to favorites...`);
            const response = await favoritesApi.addFavorite(issueId);
            console.log("Add favorite response:", response);
          } else {
            console.log(`Removing issue ${issueId} from favorites...`);
            try {
              const response = await favoritesApi.removeFavorite(issueId);
              console.log("Remove favorite response:", response);
            } catch (removeError) {
              // If it's a 404 error, it means the favorite doesn't exist
              // which is fine since we want to remove it anyway
              if (removeError instanceof ApiError && removeError.status === 404) {
                console.log(`Issue ${issueId} was not in favorites, treating as success`);
              } else {
                // Rethrow other errors
                throw removeError;
              }
            }
          }

          // After successful API call, refresh the favorites
          if (filter === "favorites") {
            console.log("Refreshing favorites list after toggle");
            // Wait a short time to ensure the backend has processed the change
            setTimeout(() => {
              // Refresh the favorites list
              const fetchFavs = async () => {
                try {
                  const response = await favoritesApi.getUserFavorites();
                  const updatedFavorites: FavoriteState = {};
                  if (response && response.items) {
                    response.items.forEach((favorite) => {
                      if (favorite && typeof favorite.issue_id === 'number') {
                        updatedFavorites[favorite.issue_id] = true;
                      }
                    });
                  }
                  setFavorites(updatedFavorites);
                  localStorage.setItem("userFavorites", JSON.stringify(updatedFavorites));
                } catch (error) {
                  console.error("Error refreshing favorites:", error);
                }
              };
              fetchFavs();
            }, 500);
          }
        } catch (apiError) {
          console.error("API error when toggling favorite:", apiError);

          // Log more detailed error information
          if (apiError instanceof Error) {
            console.error("Error details:", apiError.message);
          }

          // Store the operation in pending operations for retry
          const pendingOps: PendingOperationsState = JSON.parse(localStorage.getItem("pendingFavoriteOps") || "{}");
          pendingOps[issueId.toString()] = {
            operation: isFavorite ? "add" : "remove",
            timestamp: Date.now()
          };
          localStorage.setItem("pendingFavoriteOps", JSON.stringify(pendingOps));

          // Implement retry logic (max 3 retries)
          if (retryCount < 3) {
            console.log(`Retrying (${retryCount + 1}/3)...`);

            // Wait for a short time before retrying (exponential backoff)
            const retryDelay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s

            // Store the pending operation in localStorage for potential recovery
            const pendingOps: PendingOperationsState = JSON.parse(localStorage.getItem("pendingFavoriteOps") || "{}");
            pendingOps[issueId.toString()] = {
              operation: isFavorite ? "add" : "remove",
              timestamp: Date.now()
            };
            localStorage.setItem("pendingFavoriteOps", JSON.stringify(pendingOps));

            // Use a timeout ID that can be cleared if the component unmounts
            const timeoutId = setTimeout(() => {
              // Retry the operation
              toggleFavorite(issueId, retryCount + 1);
            }, retryDelay);

            // Store the timeout ID in a ref so it can be cleared if needed
            // This is handled in the useEffect cleanup function
            if (typeof window !== 'undefined') {
              // Store the timeout ID in a global object that can be accessed for cleanup
              window.__pendingTimeouts = window.__pendingTimeouts || {};
              window.__pendingTimeouts[`favorite_${issueId}`] = timeoutId;
            }

            return; // Exit early as we're retrying
          }

          // If we've exhausted retries, show a toast notification
          toast({
            title: "Warning",
            description: "Changes saved locally but not synced with server. Will try again later.",
            duration: 5000,
          });

          // Continue with local state changes even if API fails
          // We already updated localStorage above
        }
      }

      // Show success toast
      toast({
        title: isFavorite ? "Added to favorites" : "Removed from favorites",
        description: `Issue #${issueId} has been ${isFavorite ? "added to" : "removed from"} your favorites.`,
        duration: 3000,
      });
    } catch (error) {
      console.error("Error toggling favorite:", error);

      // Use the restoreFavoritesState helper function
      restoreFavoritesState();

      toast({
        title: "Error",
        description: "Failed to update favorites. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Helper function to restore favorites state from localStorage or revert to previous state
  const restoreFavoritesState = () => {
    try {
      const storedFavorites = localStorage.getItem("userFavorites");
      if (storedFavorites) {
        setFavorites(JSON.parse(storedFavorites));
      } else {
        // Revert to previous state if localStorage is not available
        const revertedFavorites = { ...favorites };
        setFavorites(revertedFavorites);
      }
    } catch (e) {
      // If all else fails, just revert to the previous state
      const revertedFavorites = { ...favorites };
      setFavorites(revertedFavorites);
    }
  };

  // Toggle expanded state for an issue
  const toggleExpanded = (issueId: number) => {
    setExpandedIssue(expandedIssue === issueId ? null : issueId);
  };

  // Open issue details dialog
  const openIssueDetails = (issue: MappedIssue) => {
    console.log('Opening issue details:', issue);
    console.log('Issue solution:', issue.solution);
    console.log('Jira ID:', issue.jira_id);
    console.log('Jira Link:', issue.jira_link);
    setSelectedIssue(issue);
    setIsDetailsDialogOpen(true);
  };

  // Utility functions are now imported from @/lib/utils/issue-utils

  // Render loading skeleton when favorites are loading
  if (isLoadingFavorites) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex justify-between">
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-6 w-6 rounded-full" />
              </div>
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-3/4" />
            </CardContent>
            <CardFooter className="flex justify-between pt-2">
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-4 w-1/4" />
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  // Render empty state if no issues match the filter
  if (filteredIssues.length === 0) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">No issues found</h3>
        <p className="text-muted-foreground mt-2">
          {filter === "favorites"
            ? "You haven't added any issues to your favorites yet."
            : "No issues match the current filter criteria."}
        </p>
        <div className="flex justify-center gap-2 mt-4">
          {filter === "favorites" && (
            <Button
              variant="outline"
              onClick={() => router.push("/issues?tab=all")}
            >
              View all issues
            </Button>
          )}
          {/* Refresh button */}
          <Button
            variant="outline"
            onClick={async () => {
              try {
                setSearchQuery(''); // Clear search query
                await onRefresh(); // Call the refresh function
                toast({ // Add toast for feedback
                  title: "Refreshed",
                  description: "Issues list has been refreshed.",
                });
              } catch (error) {
                toast({ // Add toast for feedback
                  title: "Error",
                  description: "Failed to refresh issues. Please try again.",
                  variant: "destructive",
                });
              }
            }}
          >
            Refresh
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-2">
        <div className="flex items-center space-x-2 w-full max-w-md">
          <div className="relative w-full">
            <input
              type="text"
              placeholder="Search issues..."
              className="w-full px-3 py-2 border rounded-md pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          </div>
          
            <LayoutList className="h-4 w-4" />
          
          
            <LayoutGrid className="h-4 w-4" />
          
        </div>
      </div>

      {viewMode === "card" ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {filteredIssues.map((issue) => (
            <Card
              key={issue.id}
              className="overflow-hidden h-full flex flex-col hover:shadow-md transition-shadow cursor-pointer border border-transparent hover:border-primary/20"
              onClick={() => openIssueDetails(issue)}
            >
              <CardHeader className="pb-2 flex-shrink-0">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <CardTitle className="text-base line-clamp-1">{issue.title}</CardTitle>
                    <CardDescription className="mt-1 text-xs">
                      {issue.error_code && (
                        <span className="font-mono bg-muted px-1 py-0.5 rounded mr-1">
                          {issue.error_code}
                        </span>
                      )}
                      <Badge variant="outline" className="text-xs">#{issue.id}</Badge>
                    </CardDescription>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent card click
                      toggleFavorite(issue.id);
                    }}
                    className="h-7 w-7"
                  >
                    <Star
                      className={`h-4 w-4 ${favorites[issue.id] ? "fill-yellow-400 text-yellow-400" : "text-muted-foreground"}`}
                    />
                  </Button>
                </div>
              </CardHeader>

              <CardContent className="pb-2 flex-grow">
                <div className="flex flex-wrap gap-1 mb-2">
                  <StatusBadge 
                    status={issue.status} 
                    statusInfo={getStatusInfo(issue.status)} 
                    className="text-xs"
                  />
                  {issue.impact && (
                    <Badge className={`${getImpactColor(issue.impact)} text-white text-xs`}>
                      {issue.impact}
                    </Badge>
                  )}
                  {issue.frequency && issue.frequency !== 'UNKNOWN' && (
                    <Badge className={`${getFrequencyInfo(issue.frequency).bgColor} ${getFrequencyInfo(issue.frequency).color} text-xs`}>
                      {getFrequencyInfo(issue.frequency).icon}
                      {issue.frequency}
                    </Badge>
                  )}
                  {issue.system_name && issue.system_name !== 'OTHERS' && (
                    <Badge className={`${getSystemInfo(issue.system_name).bgColor} ${getSystemInfo(issue.system_name).color} text-xs`}>
                      {getSystemInfo(issue.system_name).icon}
                      {issue.system_name}
                    </Badge>
                  )}
                </div>

                <p className="text-xs line-clamp-3 text-muted-foreground">
                  {issue.description}
                </p>
              </CardContent>

              <CardFooter className="pt-0 pb-3 flex justify-between items-center text-xs text-muted-foreground flex-shrink-0">
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <span>{formatDate(issue.createdAt)}</span>
                </div>
                {issue.solution && issue.solution.exists && (
                  <Badge variant="outline" className="text-xs bg-green-50">
                    Solution
                  </Badge>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        filteredIssues.map((issue) => (
        <Card key={issue.id} className="overflow-hidden">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <CardTitle className="text-lg">{issue.title}</CardTitle>
                  <Badge variant="outline" className="ml-2">#{issue.id}</Badge>
                </div>
                <CardDescription className="mt-1">
                  {issue.error_code && (
                    <span className="font-mono text-xs bg-muted px-1 py-0.5 rounded mr-2">
                      {issue.error_code}
                    </span>
                  )}
                  {issue.system_name && issue.system_name !== 'OTHERS' && (
                <Badge className={`${getSystemInfo(issue.system_name).bgColor} ${getSystemInfo(issue.system_name).color} text-xs`}>
                  {getSystemInfo(issue.system_name).icon}
                  {issue.system_name}
                </Badge>
              )}
              {issue.type && <span className="ml-2">{issue.type}</span>}
                </CardDescription>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => toggleFavorite(issue.id)}
                className="h-8 w-8"
              >
                <Star
                  className={`h-5 w-5 ${favorites[issue.id] ? "fill-yellow-400 text-yellow-400" : "text-muted-foreground"}`}
                />
              </Button>
            </div>
          </CardHeader>

          <CardContent className="pb-2">
            <div className="flex flex-wrap gap-2 mb-3">
              <StatusBadge 
                status={issue.status} 
                statusInfo={getStatusInfo(issue.status)} 
                className="text-white"
              />
              {issue.impact && (
                <Badge className={`${getImpactColor(issue.impact)} text-white`}>
                  {issue.impact}
                </Badge>
              )}
              {issue.frequency && issue.frequency !== 'UNKNOWN' && (
                <Badge className={`${getFrequencyInfo(issue.frequency).bgColor} ${getFrequencyInfo(issue.frequency).color} text-xs`}>
                  {getFrequencyInfo(issue.frequency).icon}
                  {issue.frequency}
                </Badge>
              )}
              <Badge variant="secondary">
                {issue.category}
              </Badge>
            </div>

            <p className="text-sm line-clamp-2">
              {issue.description}
            </p>

            {expandedIssue === issue.id && (
              <div className="mt-4">
                <Tabs defaultValue="details">
                  <TabsList className="mb-2">
                    <TabsTrigger value="details">Details</TabsTrigger>
                    <TabsTrigger value="solution">
                      Solution
                      {issue.solution && issue.solution.exists && (
                        <span className="ml-1 h-2 w-2 rounded-full bg-green-500 inline-block"></span>
                      )}
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="details" className="mt-0">
                    <div className="space-y-2 text-sm">
                      <p className="whitespace-pre-line">{issue.description}</p>
                      {issue.error_message && (
                        <div className="mt-2">
                          <h4 className="font-medium">Error Message:</h4>
                          <pre className="bg-muted p-2 rounded text-xs overflow-x-auto mt-1">
                            {issue.error_message}
                          </pre>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="solution" className="mt-0">
                    {issue.solution && issue.solution.exists ? (
                      <div className="space-y-2 text-sm">
                        <div className="bg-muted/50 p-3 rounded">
                          {issue.solution.content && issue.solution.content.trim() !== "" ? (
                            <p className="whitespace-pre-pre-line">{issue.solution.content}</p>
                          ) : (
                            <p className="text-muted-foreground italic">No solution content available</p>
                          )}
                        </div>
                        <div className="flex items-center justify-between text-xs text-muted-foreground mt-2">
                          <span>
                            Provided by: {issue.solution.author}
                          </span>
                          {issue.solution.createdAt && (
                            <span>
                              {formatDate(issue.solution.createdAt)}
                            </span>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-4">
                        <p className="text-muted-foreground">No solution available yet.</p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2"
                          onClick={() => {
                            // This would navigate to a solution form
                            // For now, just show a toast
                            toast({
                              title: "Add Solution",
                              description: "This would open a solution form.",
                            });
                          }}
                        >
                          Add Solution
                        </Button>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </div>
            )}
          </CardContent>

          <CardFooter className="flex justify-between pt-2 text-xs text-muted-foreground">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-muted-foreground">Error Code:</span>
              <code className="text-xs px-2 py-1 bg-muted rounded">{issue.error_code}</code>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-muted-foreground">System:</span>
              {issue.system_name && issue.system_name !== 'OTHERS' ? (
                <Badge className={`${getSystemInfo(issue.system_name).bgColor} ${getSystemInfo(issue.system_name).color} text-xs`}>
                  {getSystemInfo(issue.system_name).icon}
                  {issue.system_name}
                </Badge>
              ) : (
                <span className="text-sm">{issue.system_name}</span>
              )}
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-muted-foreground">Message:</span>
              <p className="text-sm line-clamp-2">{issue.error_message}</p>
            </div>
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => openIssueDetails(issue)}
                className="h-6 px-2"
              >
                View Details
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => toggleExpanded(issue.id)}
                className="h-6 px-2"
              >
                {expandedIssue === issue.id ? "Collapse" : "Expand"}
              </Button>
            </div>
          </CardFooter>
        </Card>
          )
          ))}

      {/* Issue Details Dialog */}
      <IssueDetailsDialog
        issue={selectedIssue}
        open={isDetailsDialogOpen}
        onOpenChange={setIsDetailsDialogOpen}
        onToggleFavorite={toggleFavorite}
        isFavorite={selectedIssue ? !!favorites[selectedIssue.id] : false}
      />
    </div>
  );
}