"use client"

import { useState, useEffect } from "react"
import { useToast } from "@/components/ui/use-toast"
import { <PERSON><PERSON>, DialogContent, DialogTitle, DialogHeader } from "@/components/ui/dialog"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Calendar, FileText, Link as LinkIcon, Image as ImageIcon, Star as StarIcon } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { cn } from "@/lib/utils"
import { KnowledgeEntry } from "@/types/knowledge"

interface Solution {
  solution_id: number;
  solution_text: string;
  created_at: string;
  provider_name?: string;
}
interface KbDetailsDialogProps {
  knowledgeEntry: KnowledgeEntry | null
  open: boolean,
  onOpenChange: (open: boolean) => void,
  onToggleFavorite: (knowledgeId: number) => void 
}

export function KbDetailsDialog({
  knowledgeEntry,
  open,
  onOpenChange,
  onToggleFavorite,
}: KbDetailsDialogProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("details")
  const [animateHeader, setAnimateHeader] = useState(false)
  const [isFavorite, setIsFavorite] = useState(false)
  const [solution, setSolution] = useState<Solution | null>(null)
  const [isLoadingSolution, setIsLoadingSolution] = useState(false)

  // Animate header on open and set favorite status
  useEffect(() => {
    if (open && knowledgeEntry) {
      setTimeout(() => setAnimateHeader(true), 100)
      // Read favorite status directly from the entry prop which should be updated by the parent
      setIsFavorite(knowledgeEntry.is_favorite || false)

      // Fetch solution if there's a solution_id
      if (knowledgeEntry.solution_id) {
        fetchSolution(knowledgeEntry.solution_id)
      } else {
        setSolution(null)
      }
    } else {
      setAnimateHeader(false)
      setSolution(null)
    }
  }, [open, knowledgeEntry]) // Depend on knowledgeEntry to update favorite status
  
  const fetchSolution = async (solutionId: number) => {
    try {
      setIsLoadingSolution(true)
      const response = await fetch(`/api/solutions/${solutionId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch solution')
      }
      const data = await response.json()
      setSolution(data)
    } catch (error) {
      console.error('Error fetching solution:', error)
      toast({
        title: "Error",
        description: "Failed to load solution",
        variant: "destructive",
      })
      setSolution(null)
    } finally {
      setIsLoadingSolution(false)
    }
  }

  if (!knowledgeEntry) {
    return null
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }
  // Extract file name from URL if present
  const getFileName = (fileUrl?: string) => {
   if (!fileUrl) return null
    const parts = fileUrl.split('/')
   return parts[parts.length - 1]
  }

  // Determine if the file is an image
  const isImageFile = (fileUrl?: string) => {
    if (!fileUrl) return false
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg']
    return imageExtensions.some(ext => fileUrl.toLowerCase().endsWith(ext))
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto p-0">
        <DialogTitle className="sr-only">{knowledgeEntry.title}</DialogTitle>
        {/* Header section with title and metadata */}
        <div
          className={cn(
            "p-6 border-b transition-all duration-500 ease-in-out",
            animateHeader ? "bg-slate-50 dark:bg-slate-900" : "bg-white dark:bg-slate-950"
          )}
        >
          <div className="pr-20">
            <div className="flex items-center gap-2 mb-2">
              <Badge
                variant="outline"
                className="px-2 py-0.5 text-xs font-medium border-slate-200 dark:border-slate-700"
              >
                KB #{knowledgeEntry.kb_id}
              </Badge>
            </div>
            <h2 className="text-xl font-bold tracking-tight mb-2 text-slate-900 dark:text-slate-50">
              {knowledgeEntry.title}
            </h2>

            <div className="flex flex-wrap items-center gap-3 text-sm text-muted-foreground">
              {knowledgeEntry.system && (
                <Badge variant="outline" className="px-2 py-0.5">
                  {knowledgeEntry.system}
                </Badge>
              )}
              {knowledgeEntry.type && (
                <Badge variant="outline" className="px-2 py-0.5">
                  {knowledgeEntry.type}
                </Badge>
              )}
            </div>
          </div>

          {/* Metadata row */}
          <div className="flex flex-wrap gap-4 mt-5 text-sm text-muted-foreground">
            <div className="flex items-center gap-1.5">
              <Calendar className="h-4 w-4" />
              <span>Created {formatDate(knowledgeEntry.created_at)}</span>
              <span className="text-xs">({formatDistanceToNow(new Date(knowledgeEntry.created_at), { addSuffix: true })})</span>
            </div>

            <div className="flex items-center gap-1.5">
              <Avatar className="h-5 w-5">
                <AvatarFallback className="text-xs">
                  {knowledgeEntry.author_name?.substring(0, 2).toUpperCase() || 'UN'}
                </AvatarFallback>
              </Avatar>
              <span> By {knowledgeEntry.author_name || 'Unknown'} </span>
            </div>

            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "ml-auto flex items-center gap-1.5 px-2.5 py-1",
                isFavorite ? "text-yellow-500 hover:text-yellow-600" : "text-muted-foreground"
              )}
            >
              <StarIcon className={cn("h-4 w-4", isFavorite ? "fill-yellow-500" : "")} />
              {isFavorite ? "Favorited" : "Add to favorites"}
            </Button>
          </div>
        </div>

        {/* Content area with ScrollArea for controlled scrolling */}
        <ScrollArea className="h-[calc(90vh-220px)] w-full">
          <div className="p-6">
            <Tabs defaultValue="details" value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="mb-6 w-full justify-start bg-slate-100 dark:bg-slate-800/50 p-1 rounded-lg">
                <TabsTrigger
                  value="details"
                  className="px-4 py-2.5 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-sm rounded-md transition-all"
                >
                  Details
                </TabsTrigger>
                {knowledgeEntry.file_url && (
                  <TabsTrigger
                    value="attachments"
                    className="px-4 py-2.5 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-sm rounded-md transition-all"
                  >
                    Attachments
                  </TabsTrigger>
                )}
                {/* Add solution tab if there's a connected solution */}
                {knowledgeEntry.solution_id && (
                  <TabsTrigger
                    value="solution"
                    className="px-4 py-2.5 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-sm rounded-md transition-all"
                  >
                    Connected Solution
                  </TabsTrigger>
                )}
              </TabsList>

              <TabsContent value="details" className="mt-0 space-y-6 animate-in fade-in-50 duration-300">
                <Card className="border border-slate-200 dark:border-slate-800 shadow-sm">
                  <CardHeader className="pb-2 bg-slate-50/50 dark:bg-slate-900/30">
                    <CardTitle className="text-base font-medium text-slate-900 dark:text-slate-100">
                      Content
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <div className="whitespace-pre-line text-sm leading-relaxed">
                      {knowledgeEntry.content}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Solution Tab Content */}
              {knowledgeEntry.file_url && (
                <TabsContent value="attachments" className="mt-0 space-y-6 animate-in fade-in-50 duration-300">
                  <Card className="border border-slate-200 dark:border-slate-800 shadow-sm">
                    <CardHeader className="pb-2 bg-slate-50/50 dark:bg-slate-900/30">
                      <CardTitle className="text-base font-medium text-slate-900 dark:text-slate-100">
                        Attached Files
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-4">
                      <div className="space-y-4">
                        <div className="flex items-center gap-3 p-3 border rounded-md bg-slate-50 dark:bg-slate-900">
                          {isImageFile(knowledgeEntry.file_url) ? (
                            <ImageIcon className="h-5 w-5 text-blue-500" />
                          ) : (
                            <FileText className="h-5 w-5 text-blue-500" />
                          )}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">{getFileName(knowledgeEntry.file_url)}</p>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            asChild
                          >
                            <a href={knowledgeEntry.file_url} target="_blank" rel="noopener noreferrer">
                              <LinkIcon className="h-4 w-4 mr-2" />
                              View
                            </a>
                          </Button>
                        </div>

                        {isImageFile(knowledgeEntry.file_url) && (
                          <div className="mt-4 border rounded-md overflow-hidden">
                            <img
                              src={knowledgeEntry.file_url}
                              alt="Attached image"
                              className="max-w-full h-auto"
                            />
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              )}

              <TabsContent value="solution" className="mt-0 space-y-6 animate-in fade-in-50 duration-300">
                <Card className="border border-slate-200 dark:border-slate-800 shadow-sm">
                  <CardHeader className="pb-2 bg-slate-50/50 dark:bg-slate-900/30">
                    <CardTitle className="text-base font-medium text-slate-900 dark:text-slate-100">
                      Connected Solution
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-4">
                    {isLoadingSolution ? (
                      <div className="flex justify-center py-4">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                      </div>
                    ) : solution ? (
                      <div className="whitespace-pre-line text-sm leading-relaxed">
                        {solution.solution_text}
                      </div>
                    ) : (
                      <p className="text-muted-foreground italic">
                        No solution has been added to this knowledge base entry yet.
                      </p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}