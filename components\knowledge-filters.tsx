"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Separator } from "@/components/ui/separator"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown, ChevronUp } from "lucide-react"

interface KnowledgeFiltersProps {
  onFiltersChange?: (filters: {
    systems: {
      oms: boolean;
      wms: boolean;
      automation: boolean;
      other: boolean;
    };
    types: {
      database: boolean;
      api: boolean;
      ui: boolean;
      workflow: boolean;
      network: boolean;
      other: boolean;
    };
    verification: string;
    sortBy: string;
  }) => void;
}

export function KnowledgeFilters({ onFiltersChange }: KnowledgeFiltersProps) {
  const [filters, setFilters] = useState({
    systems: {
      oms: false,
      wms: false,
      automation: false,
      other: false,
    },
    types: {
      database: false,
      api: false,
      ui: false,
      workflow: false,
      network: false,
      other: false,
    },
    verification: "all",
    sortBy: "relevance",
  })

  // State for collapsible sections
  const [openSections, setOpenSections] = useState({
    systems: true,
    types: true,
    verification: true,
    sortBy: true,
  })

  const toggleSection = (section: keyof typeof openSections) => {
    setOpenSections({
      ...openSections,
      [section]: !openSections[section],
    })
  }

  const handleSystemChange = (system: keyof typeof filters.systems) => {
    const newFilters = {
      ...filters,
      systems: {
        ...filters.systems,
        [system]: !filters.systems[system],
      },
    };
    setFilters(newFilters);
    if (onFiltersChange) {
      onFiltersChange(newFilters);
    }
  }

  const handleTypeChange = (type: keyof typeof filters.types) => {
    const newFilters = {
      ...filters,
      types: {
        ...filters.types,
        [type]: !filters.types[type],
      },
    };
    setFilters(newFilters);
    if (onFiltersChange) {
      onFiltersChange(newFilters);
    }
  }

  const handleVerificationChange = (verification: string) => {
    const newFilters = {
      ...filters,
      verification,
    };
    setFilters(newFilters);
    if (onFiltersChange) {
      onFiltersChange(newFilters);
    }
  }

  const handleSortByChange = (sortBy: string) => {
    const newFilters = {
      ...filters,
      sortBy,
    };
    setFilters(newFilters);
    if (onFiltersChange) {
      onFiltersChange(newFilters);
    }
  }

  const resetFilters = () => {
    const defaultFilters = {
      systems: {
        oms: false,
        wms: false,
        automation: false,
        other: false,
      },
      types: {
        database: false,
        api: false,
        ui: false,
        workflow: false,
        network: false,
        other: false,
      },
      verification: "all",
      sortBy: "relevance",
    };
    setFilters(defaultFilters);
    if (onFiltersChange) {
      onFiltersChange(defaultFilters);
    }
  }

  return (
    <div className="space-y-6">
      <Collapsible open={openSections.systems} onOpenChange={() => toggleSection("systems")}>
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">System/Module</h3>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="sm" className="p-0 h-7 w-7">
              {openSections.systems ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </Button>
          </CollapsibleTrigger>
        </div>
        <CollapsibleContent className="pt-2">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="system-oms"
                checked={filters.systems.oms}
                onCheckedChange={() => handleSystemChange("oms")}
              />
              <Label htmlFor="system-oms">Order Management System</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="system-wms"
                checked={filters.systems.wms}
                onCheckedChange={() => handleSystemChange("wms")}
              />
              <Label htmlFor="system-wms">Warehouse Management System</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="system-automation"
                checked={filters.systems.automation}
                onCheckedChange={() => handleSystemChange("automation")}
              />
              <Label htmlFor="system-automation">Automation Platform</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="system-other"
                checked={filters.systems.other}
                onCheckedChange={() => handleSystemChange("other")}
              />
              <Label htmlFor="system-other">Other</Label>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      <Separator />

      <Collapsible open={openSections.types} onOpenChange={() => toggleSection("types")}>
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">Exception Type</h3>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="sm" className="p-0 h-7 w-7">
              {openSections.types ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </Button>
          </CollapsibleTrigger>
        </div>
        <CollapsibleContent className="pt-2">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="type-database"
                checked={filters.types.database}
                onCheckedChange={() => handleTypeChange("database")}
              />
              <Label htmlFor="type-database">Database</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="type-api" checked={filters.types.api} onCheckedChange={() => handleTypeChange("api")} />
              <Label htmlFor="type-api">API</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="type-ui" checked={filters.types.ui} onCheckedChange={() => handleTypeChange("ui")} />
              <Label htmlFor="type-ui">UI</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="type-workflow"
                checked={filters.types.workflow}
                onCheckedChange={() => handleTypeChange("workflow")}
              />
              <Label htmlFor="type-workflow">Workflow</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="type-network"
                checked={filters.types.network}
                onCheckedChange={() => handleTypeChange("network")}
              />
              <Label htmlFor="type-network">Network</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="type-other"
                checked={filters.types.other}
                onCheckedChange={() => handleTypeChange("other")}
              />
              <Label htmlFor="type-other">Other</Label>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      <Separator />

      <Collapsible open={openSections.verification} onOpenChange={() => toggleSection("verification")}>
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">Verification Status</h3>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="sm" className="p-0 h-7 w-7">
              {openSections.verification ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </Button>
          </CollapsibleTrigger>
        </div>
        <CollapsibleContent className="pt-2">
          <RadioGroup value={filters.verification} onValueChange={handleVerificationChange} className="space-y-2">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="all" id="verification-all" />
              <Label htmlFor="verification-all">All</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="verified" id="verification-verified" />
              <Label htmlFor="verification-verified">Verified Only</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="community" id="verification-community" />
              <Label htmlFor="verification-community">Community Contributions</Label>
            </div>
          </RadioGroup>
        </CollapsibleContent>
      </Collapsible>

      <Separator />

      <Collapsible open={openSections.sortBy} onOpenChange={() => toggleSection("sortBy")}>
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">Sort By</h3>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="sm" className="p-0 h-7 w-7">
              {openSections.sortBy ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </Button>
          </CollapsibleTrigger>
        </div>
        <CollapsibleContent className="pt-2">
          <RadioGroup value={filters.sortBy} onValueChange={handleSortByChange} className="space-y-2">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="relevance" id="sort-relevance" />
              <Label htmlFor="sort-relevance">Relevance</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="newest" id="sort-newest" />
              <Label htmlFor="sort-newest">Newest First</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="oldest" id="sort-oldest" />
              <Label htmlFor="sort-oldest">Oldest First</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="most-viewed" id="sort-most-viewed" />
              <Label htmlFor="sort-most-viewed">Most Viewed</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="most-liked" id="sort-most-liked" />
              <Label htmlFor="sort-most-liked">Most Helpful</Label>
            </div>
          </RadioGroup>
        </CollapsibleContent>
      </Collapsible>

      <Button variant="outline" size="sm" onClick={resetFilters} className="w-full">
        Reset Filters
      </Button>
    </div>
  )
}

