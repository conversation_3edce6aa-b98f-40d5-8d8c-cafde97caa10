import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

// Mock data for live issue tracker
const liveIssues = {
  open: 12,
  inProgress: 8,
  resolved: 24,
  total: 44,
  critical: 3,
  high: 5,
  medium: 7,
  low: 5,
}

export function LiveIssueTracker() {
  const openPercentage = (liveIssues.open / liveIssues.total) * 100
  const inProgressPercentage = (liveIssues.inProgress / liveIssues.total) * 100
  const resolvedPercentage = (liveIssues.resolved / liveIssues.total) * 100

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>Total Issues</span>
          <span className="font-medium">{liveIssues.total}</span>
        </div>
        <div className="flex h-2 overflow-hidden rounded-full bg-secondary">
          <div className="bg-orange-500" style={{ width: `${openPercentage}%` }} />
          <div className="bg-blue-500" style={{ width: `${inProgressPercentage}%` }} />
          <div className="bg-green-500" style={{ width: `${resolvedPercentage}%` }} />
        </div>
        <div className="flex justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <div className="h-2 w-2 rounded-full bg-orange-500" />
            <span>Open ({liveIssues.open})</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="h-2 w-2 rounded-full bg-blue-500" />
            <span>In Progress ({liveIssues.inProgress})</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="h-2 w-2 rounded-full bg-green-500" />
            <span>Resolved ({liveIssues.resolved})</span>
          </div>
        </div>
      </div>

      <div className="space-y-3">
        <h4 className="text-sm font-medium">By Priority</h4>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Badge variant="destructive">Critical</Badge>
            </div>
            <span>{liveIssues.critical}</span>
          </div>
          <Progress value={(liveIssues.critical / liveIssues.total) * 100} className="h-2" />
        </div>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Badge className="bg-orange-500 hover:bg-orange-600">High</Badge>
            </div>
            <span>{liveIssues.high}</span>
          </div>
          <Progress value={(liveIssues.high / liveIssues.total) * 100} className="h-2" />
        </div>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Badge className="bg-yellow-500 hover:bg-yellow-600">Medium</Badge>
            </div>
            <span>{liveIssues.medium}</span>
          </div>
          <Progress value={(liveIssues.medium / liveIssues.total) * 100} className="h-2" />
        </div>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Badge className="bg-green-500 hover:bg-green-600">Low</Badge>
            </div>
            <span>{liveIssues.low}</span>
          </div>
          <Progress value={(liveIssues.low / liveIssues.total) * 100} className="h-2" />
        </div>
      </div>
    </div>
  )
}

