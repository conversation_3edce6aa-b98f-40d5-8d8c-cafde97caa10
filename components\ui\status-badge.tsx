import { cn } from "@/lib/utils"
import { StatusInfo } from "@/types/issue-details"

type StatusBadgeProps = {
  status: string | StatusEnum
  statusInfo: StatusInfo
  className?: string
}

// Import StatusEnum from the API types
import { StatusEnum } from "@/lib/api"

export const StatusBadge = ({ status, statusInfo, className }: StatusBadgeProps) => {
  return (
    <span
      className={cn(
        "px-2.5 py-1 flex items-center gap-1.5 font-medium ring-1 shadow-sm text-sm rounded-md",
        "transition-colors duration-200 ease-in-out",
        statusInfo.color,
        statusInfo.bgColor,
        statusInfo.ringColor,
        // Add hover effect - slightly darken the background on hover
        {
          'hover:bg-rose-100 dark:hover:bg-rose-900/60': status === StatusEnum.Open,
          'hover:bg-amber-100 dark:hover:bg-amber-900/60': status === StatusEnum.InProgress,
          'hover:bg-blue-100 dark:hover:bg-blue-900/60': status === StatusEnum.Resolved,
          'hover:bg-emerald-100 dark:hover:bg-emerald-800/60': status === StatusEnum.Closed,
          // Default hover for any other status
          'hover:bg-slate-100 dark:hover:bg-slate-800/60': !Object.values(StatusEnum).includes(status as StatusEnum),
        },
        className
      )}
    >
      {statusInfo.icon}
      {status}
    </span>
  )
}
