# API Client Migration Guide

This document outlines the changes made to the API client and how to migrate your code to use the new structure.

## Key Changes

1. **Modular Structure**: The API client has been split into logical modules under `lib/api/`
2. **Type Safety**: Improved TypeScript types and interfaces
3. **Consistent Naming**: Standardized method names and parameters
4. **Better Error Handling**: Improved error handling and type safety
5. **Token Management**: Automatic token refresh and session management

## Migration Steps

### 1. Update Imports

#### Before:
```typescript
import { 
  userApi, 
  projectApi, 
  issueApi, 
  solutionApi, 
  knowledgeBaseApi, 
  favoritesApi,
  StatusEnum,
  CategoryEnum,
  // ... other enums and types
} from '@/lib/api';
```

#### After:
```typescript
// Import specific API clients
import { 
  usersApi, 
  projectsApi, 
  issuesApi, 
  solutionsApi, 
  knowledgeBaseApi, 
  favoritesApi,
  StatusEnum,
  CategoryEnum,
  // ... other enums and types
} from '@/lib/api';
```

### 2. API Method Changes

#### Users API
- `userApi.getCurrentUser()` → `usersApi.getCurrentUser()`
- `userApi.getUser(id)` → `usersApi.getUser(id)`
- `userApi.updateUser(id, data)` → `usersApi.updateUser(id, data)`

#### Projects API
- `projectApi.getProjectsWithUsers()` → `projectsApi.getProjectsWithUsers()`
- `projectApi.getProjects()` → `projectsApi.getProjects()`
- `projectApi.getProject(id)` → `projectsApi.getProject(id)`
- `projectApi.createProject(data)` → `projectsApi.createProject(data)`
- `projectApi.updateProject(id, data)` → `projectsApi.updateProject(id, data)`
- `projectApi.deleteProject(id)` → `projectsApi.deleteProject(id)`
- `projectApi.getProjectUsers(projectId)` → `projectsApi.getProjectUsers(projectId)`
- `projectApi.addUserToProject(projectId, userId, role)` → `projectsApi.addUserToProject(projectId, userId, role)`
- `projectApi.removeUserFromProject(projectId, userId)` → `projectsApi.removeUserFromProject(projectId, userId)`

#### Issues API
- `issueApi.searchIssues(projectId, query, filters)` → `issuesApi.searchIssues({ project_id: projectId, query, ...filters })`
- `issueApi.getIssue(id)` → `issuesApi.getIssue(id)`
- `issueApi.createIssue(data)` → `issuesApi.createIssue(data)`
- `issueApi.updateIssue(id, data)` → `issuesApi.updateIssue(id, data)`
- `issueApi.updateIssueStatus(id, status)` → `issuesApi.updateIssueStatus(id, status)`
- `issueApi.deleteIssue(id)` → `issuesApi.deleteIssue(id)`
- `issueApi.getIssueSolutions(issueId)` → `solutionsApi.getIssueSolutions(issueId)`

#### Solutions API
- `solutionApi.getSolutions()` → `solutionsApi.getSolutions()`
- `solutionApi.getSolution(id)` → `solutionsApi.getSolution(id)`
- `solutionApi.createSolution(data)` → `solutionsApi.createSolution(data)`
- `solutionApi.updateSolution(id, data)` → `solutionsApi.updateSolution(id, data)`
- `solutionApi.upvoteSolution(id)` → `solutionsApi.upvoteSolution(id)`
- `solutionApi.deleteSolution(id)` → `solutionsApi.deleteSolution(id)`

#### Knowledge Base API
- `knowledgeBaseApi.getKnowledgeBase(projectId)` → `knowledgeBaseApi.getKnowledgeBase({ project_id: projectId })`
- `knowledgeBaseApi.searchKnowledgeBase(projectId, query)` → `knowledgeBaseApi.searchKnowledgeBase(query, { project_id: projectId })`
- `knowledgeBaseApi.getKnowledgeBaseEntry(id)` → `knowledgeBaseApi.getKnowledgeBaseEntry(id)`
- `knowledgeBaseApi.createKnowledgeBaseEntry(data)` → `knowledgeBaseApi.createKnowledgeBaseEntry(data)`
- `knowledgeBaseApi.updateKnowledgeBaseEntry(id, data)` → `knowledgeBaseApi.updateKnowledgeBaseEntry(id, data)`
- `knowledgeBaseApi.deleteKnowledgeBaseEntry(id)` → `knowledgeBaseApi.deleteKnowledgeBaseEntry(id)`

#### Favorites API
- `favoritesApi.getUserFavorites()` → `favoritesApi.getUserFavorites()`
- `favoritesApi.addFavorite(issueId)` → `favoritesApi.addFavorite(issueId)`
- `favoritesApi.removeFavorite(issueId)` → `favoritesApi.removeFavorite(favoriteId)`
- `toggleFavoriteKnowledge(kbId)` → `knowledgeBaseApi.toggleFavorite(kbId)`
- `likeKnowledgeEntry(kbId)` → `knowledgeBaseApi.toggleLike(kbId)`

### 3. Type Changes

- `ApiResponse<T>` is now `PaginatedResponse<T>` for paginated responses
- All request/response types are now more strictly typed
- Error handling now uses the `ApiError` class consistently

### 4. Error Handling

#### Before:
```typescript
try {
  const data = await someApi.someMethod();
} catch (error) {
  // Handle error
}
```

#### After:
```typescript
try {
  const data = await someApi.someMethod();
} catch (error) {
  if (error instanceof ApiError) {
    console.error(`API Error (${error.status}):`, error.message);
    if (error.isAuthError) {
      // Handle auth error (e.g., redirect to login)
    }
  } else {
    console.error('Unexpected error:', error);
  }
}
```

### 5. Pagination

Pagination is now handled consistently across all list endpoints:

```typescript
const { items, total, page, limit, total_pages } = await someApi.getList({
  page: 1,
  limit: 10,
  // other filters...
});
```

### 6. Authentication

Token refresh is now handled automatically. No need to manually refresh tokens or check for expiration.

## Migration Checklist

- [ ] Update all imports to use the new module structure
- [ ] Update method calls to match the new API signatures
- [ ] Update error handling to use the new `ApiError` class
- [ ] Test all API calls to ensure they work as expected
- [ ] Remove any manual token refresh logic
- [ ] Update any custom types to use the new type definitions

## Backward Compatibility

For a gradual migration, you can keep the old `api.ts` file and export the new API clients from it:

```typescript
// lib/api.ts (temporary compatibility layer)
export * from './api/new';
```

Then update your imports to use the new structure as you update each component.
