import { api } from './client';
import { 
  Issue, 
  Solution, 
  PaginatedResponse, 
  SearchFilters,
  CategoryEnum,
  StatusEnum,
  ImpactEnum,
  FrequencyEnum
} from './types';

const ISSUES_BASE = '/issues';

export const issueApi = {
  /**
   * Search issues with filters
   */
  searchIssues: (filters: SearchFilters) => {
    const params = new URLSearchParams();
    
    // Add filters to params if they exist
    if (filters.query) params.append('query', filters.query);
    if (filters.category) params.append('category', filters.category);
    if (filters.status) params.append('status', filters.status);
    if (filters.impact) params.append('impact', filters.impact);
    if (filters.frequency) params.append('frequency', filters.frequency);
    if (filters.min_upvotes) params.append('min_upvotes', filters.min_upvotes.toString());
    if (filters.project_id) params.append('project_id', filters.project_id.toString());
    if (filters.date_range) {
      params.append('start_date', filters.date_range[0]);
      params.append('end_date', filters.date_range[1]);
    }
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.sort_by) params.append('sort_by', filters.sort_by);
    if (filters.sort_order) params.append('sort_order', filters.sort_order);

    return api.get<PaginatedResponse<Issue>>(
      `${ISSUES_BASE}?${params.toString()}`
    );
  },

  /**
   * Get issue by ID
   */
  getIssue: (issueId: number) => api.get<Issue>(`${ISSUES_BASE}/${issueId}`),

  /**
   * Create a new issue
   */
  createIssue: (data: Omit<Issue, 'issue_id' | 'created_at' | 'created_by'>) =>
    api.post<Issue>(ISSUES_BASE, data),

  /**
   * Update issue
   */
  updateIssue: (issueId: number, data: Partial<Issue>) =>
    api.put<Issue>(`${ISSUES_BASE}/${issueId}`, data),

  /**
   * Update issue status
   */
  updateIssueStatus: (issueId: number, status: StatusEnum) =>
    api.patch<Issue>(`${ISSUES_BASE}/${issueId}/status`, { status }),

  /**
   * Delete issue
   */
  deleteIssue: (issueId: number) =>
    api.delete<{ success: boolean }>(`${ISSUES_BASE}/${issueId}`),

  /**
   * Get solutions for an issue
   */
  getIssueSolutions: (issueId: number) =>
    api.get<Solution[]>(`${ISSUES_BASE}/${issueId}/solutions`),

  /**
   * Add solution to issue
   */
  addSolution: (issueId: number, solutionText: string, category: CategoryEnum) => {
    // Ensure the category is in the correct case (uppercase) for the backend
    const normalizedCategory = category.toUpperCase() as CategoryEnum;
    return api.post<Solution>(`${ISSUES_BASE}/${issueId}/solutions`, {
      solution_text: solutionText,
      category: normalizedCategory,
    });
  },

  /**
   * Get recent issues
   */
  getRecentIssues: (limit: number = 10) =>
    api.get<Issue[]>(`${ISSUES_BASE}/recent?limit=${limit}`),

  /**
   * Get issues by project
   */
  getProjectIssues: (projectId: number, filters?: Omit<SearchFilters, 'project_id'>) =>
    issueApi.searchIssues({ ...filters, project_id: projectId }),

  /**
   * Get issue statistics
   */
  getIssueStats: (projectId?: number) => {
    const url = projectId 
      ? `${ISSUES_BASE}/stats?project_id=${projectId}`
      : `${ISSUES_BASE}/stats`;
    return api.get<{
      total: number;
      by_status: Record<StatusEnum, number>;
      by_category: Record<CategoryEnum, number>;
      by_impact: Record<ImpactEnum, number>;
      by_frequency: Record<FrequencyEnum, number>;
    }>(url);
  },
};
