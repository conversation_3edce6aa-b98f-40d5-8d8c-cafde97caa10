import { api } from './client';
import { Project, PaginatedResponse, User } from './types';

export const projectsApi = {
  /**
   * Get all projects with user counts
   */
  getProjectsWithUsers: () => api.get('/api/projects/with-users/'),

  /**
   * Get all projects with pagination
   */
  getProjects: (params?: Record<string, any>) =>
    api.get<PaginatedResponse<Project>>('/api/projects', { params }),

  /**
   * Get project by ID
   */
  getProject: (projectId: number) => api.get<Project>(`/api/projects/${projectId}`),

  /**
   * Create a new project
   */
  createProject: (data: Omit<Project, 'project_id' | 'created_at'>) =>
    api.post<Project>('/api/projects', data),

  /**
   * Update project
   */
  updateProject: (projectId: number, data: Partial<Project>) =>
    api.put<Project>(`/api/projects/${projectId}`, data),

  /**
   * Delete project
   */
  deleteProject: (projectId: number) =>
    api.delete<{ success: boolean }>(`/api/projects/${projectId}`),

  /**
   * Get users for a project
   */
  getProjectUsers: (projectId: number) =>
    api.get<User[]>(`/api/projects/${projectId}/users`),

  /**
   * Add user to project
   */
  addUserToProject: (projectId: number, userId: number, role: string = 'USER') =>
    api.post<{ success: boolean }>(`/api/projects/${projectId}/users`, { user_id: userId, role }),

  /**
   * Remove user from project
   */
  removeUserFromProject: (projectId: number, userId: number) =>
    api.delete<{ success: boolean }>(`/api/projects/${projectId}/users/${userId}`),

  /**
   * Update user role in project
   */
  updateUserRole: (projectId: number, userId: number, role: string) =>
    api.patch<{ success: boolean }>(`/api/projects/${projectId}/users/${userId}`, { role }),
};
