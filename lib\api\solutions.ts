import { api } from './client';
import { Solution, PaginatedResponse, CategoryEnum } from './types';

const SOLUTIONS_BASE = '/solutions';

export const solutionsApi = {
  /**
   * Get solution by ID
   */
  getSolution: (solutionId: number) => api.get<Solution>(`${SOLUTIONS_BASE}/${solutionId}`),

  /**
   * Create a new solution
   */
  createSolution: (data: {
    issue_id: number;
    solution_text: string;
    category: CategoryEnum;
  }) => api.post<Solution>(SOLUTIONS_BASE, data),

  /**
   * Update a solution
   */
  updateSolution: (solutionId: number, data: { solution_text: string; category?: CategoryEnum }) =>
    api.put<Solution>(`${SOLUTIONS_BASE}/${solutionId}`, data),

  /**
   * Delete a solution
   */
  deleteSolution: (solutionId: number) =>
    api.delete<{ success: boolean }>(`${SOLUTIONS_BASE}/${solutionId}`),

  /**
   * Upvote a solution
   */
  upvoteSolution: (solutionId: number) =>
    api.post<{ success: boolean; upvotes: number }>(
      `${SOLUTIONS_BASE}/${solutionId}/upvote`
    ),

  /**
   * Remove upvote from a solution
   */
  removeUpvote: (solutionId: number) =>
    api.delete<{ success: boolean; upvotes: number }>(
      `${SOLUTIONS_BASE}/${solutionId}/upvote`
    ),

  /**
   * Get solutions by user
   */
  getUserSolutions: (userId: number, params?: { page?: number; limit?: number }) =>
    api.get<PaginatedResponse<Solution>>(
      `/users/${userId}/solutions`,
      { params: params as Record<string, any> }
    ),

  /**
   * Get top solutions
   */
  getTopSolutions: (limit: number = 10) =>
    api.get<Solution[]>(`${SOLUTIONS_BASE}/top?limit=${limit}`),
};
