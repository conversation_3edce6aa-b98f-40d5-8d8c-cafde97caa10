import { api } from './client';
import { User, PaginatedResponse } from './types';

// Use relative path since API client will prepend the base URL
const USERS_BASE = '/users';

export const usersApi = {
  /**
   * Get current user profile
   */
  getCurrentUser: () => api.get<User>('/me'),

  /**
   * Get user by ID
   */
  getUser: (userId: number) => api.get<User>(`${USERS_BASE}/${userId}`),

  /**
   * Update user profile
   */
  updateUser: (userId: number, userData: Partial<User>) =>
    api.put<User>(`${USERS_BASE}/${userId}`, userData),

  /**
   * Get all users (admin only)
   */
  getAllUsers: (params?: { page?: number; limit?: number }) => {
    // Ensure we're not duplicating the base URL
    return api.get<PaginatedResponse<User>>(USERS_BASE, { params: params as Record<string, any> });
  },

  /**
   * Delete a user (admin only)
   */
  deleteUser: (userId: number) => api.delete<{ success: boolean }>(`${USERS_BASE}/${userId}`),

  /**
   * Update user role (admin only)
   */
  updateUserRole: (userId: number, role: string) =>
    api.patch<User>(`${USERS_BASE}/${userId}/role`, { role }),
};
