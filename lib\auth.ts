import { JWT } from "next-auth/jwt";
import { NextAuthOptions, User, Session, DefaultSession } from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";

declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      name: string ;
      accessToken: string;
      role: string;
      is_admin: boolean;
      email?: string ;
      image?: string ;
    };
  }
}



interface ExtendedUser extends User {
  accessToken: string;
  role: string;
  is_admin: boolean;
  name: string;
}

const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || 'http://localhost:8000';

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        username: { label: "Email", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials): Promise<ExtendedUser | null> {
        if (!credentials?.username || !credentials?.password) {
          return null;
        }

        try {
          const response = await fetch(`${API_BASE_URL}/token`, {
            method: "POST",
            headers: { "Content-Type": "application/x-www-form-urlencoded" },
            body: new URLSearchParams({
              username: credentials.username,
              password: credentials.password,
            }),
            credentials: 'include',
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error("Auth error:", errorData);
            return null;
          }

          const data = await response.json();
          console.log('Auth response data:', data);
          
          const isAdmin = data.is_admin === true;
          console.log('Auth - admin status:', { isAdmin, dataIsAdmin: data.is_admin });

          return {
            id: data.user_id.toString(),
            email: credentials.username,
            name: data.name || credentials.username,
            accessToken: data.access_token,
            role: isAdmin ? 'admin' : 'user',
            is_admin: isAdmin,
          };
        } catch (error) {
          console.error("Auth error:", error);
          return null;
        }
      }
    })
  ],
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      // Handle initial sign in
      if (user) {
        const extendedUser = user as ExtendedUser;
        const isAdmin = extendedUser.role === 'admin' || extendedUser.is_admin === true;
        
        return {
          ...token,
          accessToken: extendedUser.accessToken,
          role: extendedUser.role,
          is_admin: isAdmin,
          name: extendedUser.name,
        } as JWT;
      }

      if (trigger === "update" && session?.user?.accessToken) {
        return {
          ...token,
          accessToken: session.user.accessToken,
        };
      }

      return token;
    },
    async session({ session, token }): Promise<Session> {
      const isAdmin = token.role === 'admin' || token.is_admin === true;
      
      return {
        ...session,
        user: {
          ...session.user,
          id: token.sub || "",
          name: token.name || "",
          accessToken: token.accessToken as string,
          role: token.role as string,
          is_admin: isAdmin,
          email: session.user.email || null,
          image: session.user.image || null
        },
        expires: session.expires
      };
    }
  },
  pages: {
    signIn: "/auth/login",
    signOut: "/auth/login",
    error: "/auth/login",
  },
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
  },
  debug: process.env.NODE_ENV === "development",
};