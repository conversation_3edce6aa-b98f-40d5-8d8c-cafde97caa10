import { formatDistanceToNow } from "date-fns";
import { AlertCircle, CheckCircle2, Clock, Info, X, AlertTriangle, Zap, Database, Cpu, Package, Server, Layers } from "lucide-react";
import { StatusEnum, SystemNameEnum, FrequencyEnum } from "@/lib/api/types";
import React from "react";

// Types
export interface StatusInfo {
  color: string;
  bgColor: string;
  ringColor: string;
  icon: React.ReactNode;
}

export interface ImpactInfo {
  color: string;
  ringColor: string;
  icon: React.ReactNode;
}

export interface PendingOperation {
  operation: 'add' | 'remove';
  timestamp: number;
}

export interface PendingOperationsState {
  [key: string]: PendingOperation;
}

export interface FavoriteState {
  [key: number]: boolean;
}

// Status and Impact Utilities
export const getStatusInfo = (status: StatusEnum): StatusInfo => {
  const statusMap: Record<string, StatusInfo> & { default: StatusInfo } = {
    [StatusEnum.Open]: {
      color: 'text-rose-700 dark:text-rose-400',
      bgColor: 'bg-rose-50 dark:bg-rose-950/40',
      ringColor: 'ring-rose-200 dark:ring-rose-800',
      icon: React.createElement(AlertCircle, { className: 'h-4 w-4' }),
    },
    [StatusEnum.InProgress]: {
      color: 'text-amber-700 dark:text-amber-400',
      bgColor: 'bg-amber-50 dark:bg-amber-950/40',
      ringColor: 'ring-amber-200 dark:ring-amber-800',
      icon: React.createElement(Clock, { className: 'h-4 w-4' }),
    },
    [StatusEnum.Resolved]: {
      color: 'text-blue-700 dark:text-blue-400',
      bgColor: 'bg-blue-50 dark:bg-blue-950/40',
      ringColor: 'ring-blue-200 dark:ring-blue-800',
      icon: React.createElement(CheckCircle2, { className: 'h-4 w-4' }),
    },
    [StatusEnum.Closed]: {
      color: 'text-emerald-700 dark:text-emerald-400',
      bgColor: 'bg-emerald-50 dark:bg-emerald-900/60',
      ringColor: 'ring-emerald-200 dark:ring-emerald-800',
      icon: React.createElement(Info, { className: 'h-4 w-4' }),
    },
    default: {
      color: 'text-slate-700 dark:text-slate-400',
      bgColor: 'bg-slate-50 dark:bg-slate-900/60',
      ringColor: 'ring-slate-200 dark:ring-slate-800',
      icon: React.createElement(Info, { className: 'h-4 w-4' }),
    },
  };

  return statusMap[status] || statusMap.default;
};

export const getImpactInfo = (impact: string): StatusInfo => {
  const impactMap: Record<string, StatusInfo> & { default: StatusInfo } = {
    critical: {
      color: 'text-rose-700 dark:text-rose-400',
      bgColor: 'bg-rose-50 dark:bg-rose-950/40',
      ringColor: 'ring-rose-200 dark:ring-rose-800',
      icon: React.createElement(AlertTriangle, { className: 'h-4 w-4' }),
    },
    high: {
      color: 'text-orange-700 dark:text-orange-400',
      bgColor: 'bg-orange-50 dark:bg-orange-950/40',
      ringColor: 'ring-orange-200 dark:ring-orange-800',
      icon: React.createElement(AlertTriangle, { className: 'h-4 w-4' }),
    },
    medium: {
      color: 'text-amber-700 dark:text-amber-400',
      bgColor: 'bg-amber-50 dark:bg-amber-950/40',
      ringColor: 'ring-amber-200 dark:ring-amber-800',
      icon: React.createElement(AlertCircle, { className: 'h-4 w-4' }),
    },
    low: {
      color: 'text-emerald-700 dark:text-emerald-400',
      bgColor: 'bg-emerald-50 dark:bg-emerald-950/40',
      ringColor: 'ring-emerald-200 dark:ring-emerald-800',
      icon: React.createElement(Info, { className: 'h-4 w-4' }),
    },
    default: {
      color: 'text-sky-700 dark:text-sky-400',
      bgColor: 'bg-sky-50 dark:bg-sky-950/40',
      ringColor: 'ring-sky-200 dark:ring-sky-800',
      icon: React.createElement(Info, { className: 'h-4 w-4' }),
    },
  };

  return impactMap[impact.toLowerCase()] || impactMap.default;
};

// Simple color utilities (kept for backward compatibility)
export function getStatusColor(status?: StatusEnum | string | null): string {
  if (!status) return 'bg-gray-500';
  const statusInfo = getStatusInfo(status as StatusEnum);
  return `${statusInfo?.color || ''} ${statusInfo?.bgColor || ''} ${statusInfo?.ringColor || ''}`.trim() || 'bg-gray-500';
}

export function getImpactColor(impact: string): string {
  const impactInfo = getImpactInfo(impact);
  return `${impactInfo.color || ''} ${impactInfo.bgColor || ''} ${impactInfo.ringColor || ''}`.trim();
}

export const getFrequencyInfo = (frequency: FrequencyEnum | string): StatusInfo => {
  const frequencyMap: Record<string, StatusInfo> & { default: StatusInfo } = {
    [FrequencyEnum.Rare]: {
      color: 'text-violet-700 dark:text-violet-400',
      bgColor: 'bg-violet-50 dark:bg-violet-950/40',
      ringColor: 'ring-violet-200 dark:ring-violet-800',
      icon: React.createElement(Zap, { className: 'h-4 w-4' }),
    },
    [FrequencyEnum.Occasional]: {
      color: 'text-blue-700 dark:text-blue-400',
      bgColor: 'bg-blue-50 dark:bg-blue-950/40',
      ringColor: 'ring-blue-200 dark:ring-blue-800',
      icon: React.createElement(Zap, { className: 'h-4 w-4' }),
    },
    [FrequencyEnum.Frequent]: {
      color: 'text-amber-700 dark:text-amber-400',
      bgColor: 'bg-amber-50 dark:bg-amber-950/40',
      ringColor: 'ring-amber-200 dark:ring-amber-800',
      icon: React.createElement(Zap, { className: 'h-4 w-4' }),
    },
    [FrequencyEnum.Always]: {
      color: 'text-rose-700 dark:text-rose-400',
      bgColor: 'bg-rose-50 dark:bg-rose-950/40',
      ringColor: 'ring-rose-200 dark:ring-rose-800',
      icon: React.createElement(Zap, { className: 'h-4 w-4' }),
    },
    default: {
      color: 'text-slate-700 dark:text-slate-400',
      bgColor: 'bg-slate-50 dark:bg-slate-900/60',
      ringColor: 'ring-slate-200 dark:ring-slate-800',
      icon: React.createElement(Zap, { className: 'h-4 w-4' }),
    },
  };
  return frequencyMap[frequency] || frequencyMap.default;
};

export const getSystemInfo = (system: SystemNameEnum | string): StatusInfo => {
  const systemMap: Record<string, StatusInfo> & { default: StatusInfo } = {
    [SystemNameEnum.OMS]: {
      color: 'text-indigo-700 dark:text-indigo-400',
      bgColor: 'bg-indigo-50 dark:bg-indigo-950/40',
      ringColor: 'ring-indigo-200 dark:ring-indigo-800',
      icon: React.createElement(Package, { className: 'h-4 w-4' }),
    },
    [SystemNameEnum.WMS]: {
      color: 'text-emerald-700 dark:text-emerald-400',
      bgColor: 'bg-emerald-50 dark:bg-emerald-950/40',
      ringColor: 'ring-emerald-200 dark:ring-emerald-800',
      icon: React.createElement(Database, { className: 'h-4 w-4' }),
    },
    [SystemNameEnum.AUTOMATION]: {
      color: 'text-pink-700 dark:text-pink-400',
      bgColor: 'bg-pink-50 dark:bg-pink-950/40',
      ringColor: 'ring-pink-200 dark:ring-pink-800',
      icon: React.createElement(Cpu, { className: 'h-4 w-4' }),
    },
    [SystemNameEnum.OTHERS]: {
      color: 'text-slate-700 dark:text-slate-400',
      bgColor: 'bg-slate-50 dark:bg-slate-900/60',
      ringColor: 'ring-slate-200 dark:ring-slate-800',
      icon: React.createElement(Server, { className: 'h-4 w-4' }),
    },
    default: {
      color: 'text-slate-700 dark:text-slate-400',
      bgColor: 'bg-slate-50 dark:bg-slate-900/60',
      ringColor: 'ring-slate-200 dark:ring-slate-800',
      icon: React.createElement(Layers, { className: 'h-4 w-4' }),
    },
  };
  return systemMap[system] || systemMap.default;
};

export function getFrequencyColor(frequency: string): string {
  const frequencyInfo = getFrequencyInfo(frequency);
  return `${frequencyInfo.color || ''} ${frequencyInfo.bgColor || ''} ${frequencyInfo.ringColor || ''}`.trim();
}

export function getSystemColor(system: string): string {
  const systemInfo = getSystemInfo(system);
  return `${systemInfo.color || ''} ${systemInfo.bgColor || ''} ${systemInfo.ringColor || ''}`.trim();
}

// Date formatting
export function formatDate(dateString: string): string {
  try {
    const date = new Date(dateString);
    return formatDistanceToNow(date, { addSuffix: true });
  } catch (error) {
    return "Invalid date";
  }
}

// Helper functions
export const getInitials = (name: string): string => {
  return name
    .split(" ")
    .map((part) => part[0])
    .join("")
    .toUpperCase()
    .substring(0, 2);
};