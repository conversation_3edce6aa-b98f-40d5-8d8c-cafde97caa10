import { StatusEnum, SystemNameEnum } from "@/lib/api/types";

export interface Issue {
  id: number;
  title: string;
  error_code: string;
  error_message: string;
  system_name: SystemNameEnum;
  issue_type: string;
  status: StatusEnum;
  impact: string;
  frequency: string;
  category: string;
  description: string;
  created_by: string;
  created_at: string;
  jira_id?: string | null;
  jira_link?: string | null;
  hemants_view?: string | null;
  solution: {
    exists: boolean;
    content?: string;
    verified?: boolean;
    author?: string;
    created_at?: string;
  };
}

// Alias for backward compatibility
export interface MappedIssue {
  id: number;
  title: string;
  error_code?: string;
  error_message?: string;
  system_name: SystemNameEnum;
  issue_type?: string;
  status: StatusEnum;
  impact?: string;
  frequency?: string;
  category?: string;
  description: string;
  createdBy: string;
  createdAt: string;
  jira_id?: string | null;
  jira_link?: string | null;
  hemants_view?: string | null;
  solution: {
    exists: boolean;
    content?: string;
    verified?: boolean;
    author?: string;
    createdAt?: string;
  };
}

export interface IssueDetailsDialogProps {
  issue: MappedIssue | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onToggleFavoriteAction: (issueId: number) => Promise<void>;
  isFavorite: boolean;
}

// Re-export StatusInfo from utils for consistency
export type { StatusInfo } from "@/lib/utils/issue-utils";