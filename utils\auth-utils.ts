import { getSession, signOut } from 'next-auth/react';

// Base API URL from environment variables or default to local development server
const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || 'http://localhost:8000';
const REFRESH_ENDPOINT = '/refresh-token';

// Type definitions
interface TokenResponse {
  access_token: string;
  refresh_token?: string;
  token_type?: string;
  expires_in?: number;
}

interface UserSession {
  user: {
    id: string;
    name: string | null;
    email: string | null;
    image: string | null;
    accessToken: string;
    refreshToken: string;
    role: string;
    is_admin: boolean;
  };
  expires: string;
  error?: string;
}

// Track if we're already refreshing the token to prevent multiple refresh attempts
let isRefreshing = false;
let refreshSubscribers: ((token: string) => void)[] = [];

/**
 * Subscribes to token refresh events
 * @param callback - Function to be called with the new token
 * @returns Unsubscribe function
 */
function subscribeTokenRefresh(callback: (token: string) => void): () => void {
  refreshSubscribers.push(callback);
  
  // Return unsubscribe function
  return () => {
    refreshSubscribers = refreshSubscribers.filter(cb => cb !== callback);
  };
}

/**
 * Notifies all subscribers with the new token
 * @param token - The new access token
 */
function onRefreshed(token: string): void {
  refreshSubscribers.forEach(cb => cb(token));
  refreshSubscribers = [];
}

/**
 * Handles API responses and parses JSON
 * @template T - Expected response type
 * @param response - The fetch Response object
 * @returns Parsed response data
 */
export async function handleResponse<T>(response: Response): Promise<T> {
  // Handle empty responses (like 204 No Content)
  if (response.status === 204) {
    return undefined as unknown as T;
  }

  // Parse JSON response
  let data;
  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    data = await response.json().catch(() => ({}));
  } else {
    data = await response.text();
  }

  // Handle error responses
  if (!response.ok) {
    const error = new Error(
      data?.message || data?.detail || `HTTP error! status: ${response.status}`
    );
    (error as any).status = response.status;
    (error as any).data = data;
    throw error;
  }

  return data as T;
}

/**
 * Refreshes the access token using the refresh token
 * @returns Promise that resolves to the new access token or null if refresh failed
 */
export async function refreshToken(): Promise<string | null> {
  // If already refreshing, return a promise that resolves when refresh is complete
  if (isRefreshing) {
    return new Promise((resolve) => {
      const unsubscribe = subscribeTokenRefresh((token) => {
        unsubscribe();
        resolve(token);
      });
    });
  }

  isRefreshing = true;
  // Get the current session
  const currentSession = await getSession();
  if (!currentSession) {
    throw new Error('No active session');
  }

  // Get the refresh token from the session
  const refreshToken = currentSession.user.refreshToken;
  if (!refreshToken) {
    throw new Error('No refresh token available');
  }

  try {
    // Try to refresh using HTTP-only cookie first
    const refreshHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(currentSession.user.accessToken ? { 'Authorization': `Bearer ${currentSession.user.accessToken}` } : {})
    };

    let response = await fetch(`${API_BASE_URL}${REFRESH_ENDPOINT}`, {
      method: 'POST',
      credentials: 'include',
      headers: refreshHeaders
    });

    // If cookie refresh fails, try with the token in the body
    if (!response.ok) {
      console.log('Cookie refresh failed, trying with body token');
      response = await fetch(`${API_BASE_URL}${REFRESH_ENDPOINT}`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({ refresh_token: refreshToken }),
      });
    }

    // If refresh failed, log the user out
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Token refresh failed:', errorData);
      throw new Error(errorData.detail || 'Failed to refresh token');
    }

    const data: TokenResponse = await response.json();
    
    if (!data.access_token) {
      throw new Error('No access token in refresh response');
    }

    // Update the session with the new tokens
    // The session will be updated in the NextAuth.js callbacks
    // We just need to notify subscribers with the new access token
    onRefreshed(data.access_token);

    return data.access_token;
  } catch (error) {
    console.error('Failed to refresh token:', error);
    
    // Only redirect if we're in the browser
    if (typeof window !== 'undefined') {
      await signOut({ redirect: false });
      window.location.href = '/auth/login';
    }
    
    throw error;
  } finally {
    isRefreshing = false;
  }
}

/**
 * Makes an authenticated API request with automatic token refresh
 * @template T - Expected response type
 * @param endpoint - API endpoint (without base URL)
 * @param options - Fetch options
 * @returns Promise that resolves to the response data
 */
export async function apiRequest<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  // Get current session and token
  const session = await getSession() as UserSession | null;
  let token: string | undefined | null = session?.user?.accessToken;
  
  // Prepare headers
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` }),
    ...options.headers,
  };

  // Helper function to make the actual request
  const makeRequest = async (): Promise<Response> => {
    try {
      const requestHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...(options.headers as Record<string, string> || {})
      };
      
      return await fetch(`${API_BASE_URL}${endpoint}`, {
        ...options,
        headers: requestHeaders,
        credentials: 'include',
      });
    } catch (error) {
      console.error('Network error during API request:', error);
      throw new Error('Network error. Please check your connection.');
    }
  };

  try {
    let response = await makeRequest();

    // If unauthorized, try to refresh the token and retry once
    if (response.status === 401) {
      try {
        token = await refreshToken();
        if (token) {
          // Retry with the new token
          response = await makeRequest();
        }
      } catch (refreshError) {
        console.error('Failed to refresh token:', refreshError);
        // If we're in the browser, redirect to login
        if (typeof window !== 'undefined') {
          await signOut({ redirect: false });
          window.location.href = '/auth/login';
        }
        throw new Error('Session expired. Please log in again.');
      }
    }

    // Handle the response
    return handleResponse<T>(response);
  } catch (error) {
    console.error(`API request to ${endpoint} failed:`, error);
    throw error;
  }
}

// Helper for GET requests
export const apiGet = <T = any>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<T> =>
  apiRequest<T>(endpoint, { ...options, method: 'GET' });

// Helper for POST requests
export const apiPost = <T = any>(
  endpoint: string, 
  data?: any, 
  options: RequestInit = {}
): Promise<T> =>
  apiRequest<T>(endpoint, {
    ...options,
    method: 'POST',
    ...(data && { body: JSON.stringify(data) }),
  });

// Helper for PUT requests
export const apiPut = <T = any>(
  endpoint: string, 
  data?: any, 
  options: RequestInit = {}
): Promise<T> =>
  apiRequest<T>(endpoint, {
    ...options,
    method: 'PUT',
    ...(data && { body: JSON.stringify(data) }),
  });

// Helper for PATCH requests
export const apiPatch = <T = any>(
  endpoint: string, 
  data?: any, 
  options: RequestInit = {}
): Promise<T> =>
  apiRequest<T>(endpoint, {
    ...options,
    method: 'PATCH',
    ...(data && { body: JSON.stringify(data) }),
  });

// Helper for DELETE requests
export const apiDelete = <T = any>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<T> =>
  apiRequest<T>(endpoint, { ...options, method: 'DELETE' });
